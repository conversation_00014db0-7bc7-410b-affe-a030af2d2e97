"""
Tab per le coppe nazionali
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, 
    QSplitter, QTextEdit
)
from PyQt6.QtCore import Qt
from typing import Optional
from ..widgets.matches_table import MatchesTableWidget
from src.competitions.competition import CompetitionManager
from src.competitions.calendar import MatchCalendar


class CupsTabWidget(QWidget):
    """Widget tab per le coppe nazionali"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.competition_manager: Optional[CompetitionManager] = None
        self.calendar: Optional[MatchCalendar] = None
        self.setup_ui()
    
    def setup_ui(self):
        """Imposta la tab per le coppe nazionali"""
        layout = QVBoxLayout(self)
        
        # Splitter per le coppe
        cups_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Coppa Italia
        coppa_italia_group = QGroupBox("🏆 Coppa Italia")
        coppa_layout = QVBoxLayout(coppa_italia_group)
        
        self.coppa_italia_info = QTextEdit("Dati Coppa Italia non ancora disponibili.")
        self.coppa_italia_info.setReadOnly(True)
        self.coppa_italia_info.setMaximumHeight(120)
        coppa_layout.addWidget(self.coppa_italia_info)
        
        self.coppa_italia_matches = MatchesTableWidget()
        coppa_layout.addWidget(self.coppa_italia_matches)
        
        cups_splitter.addWidget(coppa_italia_group)
        
        # Supercoppa Italiana
        supercoppa_group = QGroupBox("🏆 Supercoppa Italiana")
        supercoppa_layout = QVBoxLayout(supercoppa_group)
        
        self.supercoppa_info = QTextEdit("Dati Supercoppa non ancora disponibili.")
        self.supercoppa_info.setReadOnly(True)
        self.supercoppa_info.setMaximumHeight(120)
        supercoppa_layout.addWidget(self.supercoppa_info)
        
        self.supercoppa_matches = MatchesTableWidget()
        supercoppa_layout.addWidget(self.supercoppa_matches)
        
        cups_splitter.addWidget(supercoppa_group)
        
        # Coppa Italia Serie C
        coppa_c_group = QGroupBox("🥉 Coppa Italia Serie C")
        coppa_c_layout = QVBoxLayout(coppa_c_group)
        
        self.coppa_c_info = QTextEdit("Dati Coppa Italia Serie C non ancora disponibili.")
        self.coppa_c_info.setReadOnly(True)
        self.coppa_c_info.setMaximumHeight(120)
        coppa_c_layout.addWidget(self.coppa_c_info)
        
        self.coppa_c_matches = MatchesTableWidget()
        coppa_c_layout.addWidget(self.coppa_c_matches)
        
        cups_splitter.addWidget(coppa_c_group)
        
        layout.addWidget(cups_splitter)
    
    def update_cups_display(self, competition_manager: CompetitionManager, 
                           calendar: MatchCalendar):
        """Aggiorna visualizzazione coppe"""
        self.competition_manager = competition_manager
        self.calendar = calendar
        
        # Coppa Italia
        coppa_italia = competition_manager.get_competition("Coppa Italia")
        if coppa_italia:
            from src.competitions.cup_competitions import CupCompetition
            if isinstance(coppa_italia, CupCompetition):
                stats = coppa_italia.get_competition_stats()
                coppa_text = f"<b>Fase attuale:</b> {stats.get('current_round', 'Non iniziata')}<br>"
                coppa_text += f"<b>Squadre rimanenti:</b> {stats.get('teams_remaining', 0)}<br>"
                coppa_text += f"<b>Squadre eliminate:</b> {stats.get('teams_eliminated', 0)}<br>"

                if stats.get('winner'):
                    coppa_text += f"<b>🏆 VINCITORE:</b> {stats['winner']}"
                else:
                    coppa_text += "<b>Prossimi turni:</b> Ottavi di Finale"
                
                self.coppa_italia_info.setHtml(coppa_text)
                
                # Carica partite Coppa Italia
                coppa_matches = calendar.get_matches_by_competition("Coppa Italia")
                self.coppa_italia_matches.load_matches(coppa_matches[:10])

        # Supercoppa Italiana
        supercoppa = competition_manager.get_competition("Supercoppa Italiana")
        if supercoppa:
            from src.competitions.cup_competitions import CupCompetition
            if isinstance(supercoppa, CupCompetition):
                stats = supercoppa.get_competition_stats()
                super_text = "<b>Squadre qualificate:</b><br>"
                
                # Mostra squadre qualificate per semifinali
                semifinali = supercoppa.qualified_teams
                if semifinali:
                    for round_name, teams in semifinali.items():
                        if "Semi" in round_name:
                            super_text += f"  {round_name}: {' vs '.join(teams)}<br>"
                
                if stats.get('winner'):
                    super_text += f"<br><b>🏆 VINCITORE:</b> {stats['winner']}"
                else:
                    super_text += "<br><b>Finale prevista:</b> 22 dicembre 2025 (Riyadh)"
                
                self.supercoppa_info.setHtml(super_text)
                
                # Carica partite Supercoppa
                super_matches = calendar.get_matches_by_competition("Supercoppa Italiana")
                self.supercoppa_matches.load_matches(super_matches[:10])

        # Coppa Italia Serie C
        coppa_c = competition_manager.get_competition("Coppa Italia Serie C")
        if coppa_c:
            from src.competitions.cup_competitions import CupCompetition
            if isinstance(coppa_c, CupCompetition):
                stats = coppa_c.get_competition_stats()
                coppa_c_text = f"<b>Fase attuale:</b> {stats.get('current_round', 'Non iniziata')}<br>"
                coppa_c_text += f"<b>Squadre partecipanti:</b> {len(coppa_c.teams)}<br>"
                coppa_c_text += f"<b>Squadre rimanenti:</b> {stats.get('teams_remaining', len(coppa_c.teams))}<br>"
                
                if stats.get('winner'):
                    coppa_c_text += f"<b>🏆 VINCITORE:</b> {stats['winner']}"
                else:
                    coppa_c_text += "<b>Prossimi turni:</b> Primo Turno"
                
                self.coppa_c_info.setHtml(coppa_c_text)
                
                # Carica partite Coppa Italia Serie C
                coppa_c_matches = calendar.get_matches_by_competition("Coppa Italia Serie C")
                self.coppa_c_matches.load_matches(coppa_c_matches[:10])
