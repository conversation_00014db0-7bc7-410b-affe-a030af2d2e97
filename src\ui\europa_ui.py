"""
Interfaccia utente per competizioni europee UEFA
"""
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                            QTableWidgetItem, QHeaderView, QLabel, QPushButton,
                            QTabWidget, QGroupBox, QSplitter, QTextEdit,
                            QComboBox, QProgressBar, QScrollArea)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from ..core.config import COLOR_PRIMARY, COLOR_SUCCESS, COLOR_WARNING, COLOR_ERROR, HEADER_FONT_SIZE
from ..core.utils import logger
from ..competitions.uefa_competitions import UEFACompetitionManager, LeaguePhaseStanding
from typing import List, Optional, Dict

class UEFAStandingsTableWidget(QTableWidget):
    """Tabella classifiche UEFA League Phase"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_table()

    def setup_table(self):
        """Configura tabella classifiche UEFA"""
        headers = ["Pos", "Squadra", "Paese", "G", "V", "N", "S", "GF", "GS", "DR", "Pt", "Zona"]
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)

        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.setSortingEnabled(False)

        # Ridimensiona colonne
        header = self.horizontalHeader()
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # Nome squadra
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # Paese

    def load_uefa_standings(self, standings: List[LeaguePhaseStanding]):
        """Carica classifica League Phase UEFA"""
        self.setRowCount(len(standings))

        for row, standing in enumerate(standings):
            # Posizione
            pos_item = QTableWidgetItem(str(standing.position))
            pos_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.setItem(row, 0, pos_item)

            # Nome squadra
            team_item = QTableWidgetItem(standing.team_name)
            self.setItem(row, 1, team_item)

            # Paese
            country_item = QTableWidgetItem(standing.country)
            country_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.setItem(row, 2, country_item)

            # Statistiche
            stats = [
                str(standing.matches_played),
                str(standing.wins),
                str(standing.draws),
                str(standing.losses),
                str(standing.goals_for),
                str(standing.goals_against),
                str(standing.goal_difference),
                str(standing.points)
            ]

            for col, stat in enumerate(stats, start=3):
                item = QTableWidgetItem(stat)
                item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.setItem(row, col, item)

            # Zona qualificazione
            zone_text = self._get_qualification_zone_text(standing.position)
            zone_item = QTableWidgetItem(zone_text)
            zone_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.setItem(row, 11, zone_item)

            # Colora righe per zone
            self._color_row_by_position(row, standing.position)

    def _get_qualification_zone_text(self, position: int) -> str:
        """Restituisce testo zona qualificazione"""
        if position <= 8:
            return "R16"  # Round of 16
        elif position <= 24:
            return "PO"   # Playoff
        else:
            return "OUT"  # Eliminated

    def _color_row_by_position(self, row: int, position: int):
        """Colora riga in base alla posizione"""
        from PyQt6.QtGui import QColor

        if position <= 8:
            # Zona qualificazione diretta agli ottavi (verde)
            color = QColor(144, 238, 144)  # LightGreen
        elif position <= 24:
            # Zona playoff (giallo)
            color = QColor(255, 255, 224)  # LightYellow
        else:
            # Zona eliminazione (rosso chiaro)
            color = QColor(255, 182, 193)  # LightPink

        for col in range(self.columnCount()):
            if self.item(row, col):
                self.item(row, col).setBackground(color)

class UEFAMatchesTableWidget(QTableWidget):
    """Tabella partite UEFA"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.matches_data: List[Dict] = []  # Store UEFA matches for details
        self.setup_table()

        # Collegamento doppio click per dettagli
        self.doubleClicked.connect(self.show_uefa_match_details)

    def setup_table(self):
        """Configura tabella partite UEFA"""
        headers = ["MD", "Casa", "", "Trasferta", "Competizione", "Spettatori"]
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)

        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)

        # Ridimensiona colonne
        header = self.horizontalHeader()
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # Casa
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)  # Trasferta

    def load_uefa_matches(self, matches_data: List[Dict]):
        """Carica partite UEFA"""
        self.matches_data = matches_data  # Store for details dialog
        self.setRowCount(len(matches_data))

        for row, match in enumerate(matches_data):
            # Matchday
            md_item = QTableWidgetItem(str(match.get("matchday", "")))
            md_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.setItem(row, 0, md_item)

            # Casa
            home_item = QTableWidgetItem(match.get("home_team", ""))
            self.setItem(row, 1, home_item)

            # Risultato
            home_score = match.get("home_score", "")
            away_score = match.get("away_score", "")
            result_text = f"{home_score}-{away_score}" if home_score != "" and away_score != "" else "-"
            result_item = QTableWidgetItem(result_text)
            result_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.setItem(row, 2, result_item)

            # Trasferta
            away_item = QTableWidgetItem(match.get("away_team", ""))
            self.setItem(row, 3, away_item)

            # Competizione
            comp_item = QTableWidgetItem(match.get("competition", ""))
            comp_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.setItem(row, 4, comp_item)

            # Spettatori
            attendance = match.get("attendance", 0)
            attendance_text = f"{attendance:,}" if attendance > 0 else "-"
            attendance_item = QTableWidgetItem(attendance_text)
            attendance_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.setItem(row, 5, attendance_item)

    def show_uefa_match_details(self, index):
        """Mostra dettagli partita UEFA al doppio click"""
        if not self.matches_data:
            return

        row = index.row()
        if 0 <= row < len(self.matches_data):
            match_data = self.matches_data[row]

            # Creiamo un oggetto Match temporaneo per la dialog
            from ..competitions.calendar import Match
            import datetime

            temp_match = Match(
                home_team=match_data.get("home_team", ""),
                away_team=match_data.get("away_team", ""),
                date=datetime.date.today(),  # Placeholder
                time="20:45",
                matchday=match_data.get("matchday", 1),
                competition=match_data.get("competition", "UEFA")
            )

            temp_match.home_score = match_data.get("home_score", 0)
            temp_match.away_score = match_data.get("away_score", 0)
            temp_match.played = True
            temp_match.attendance = match_data.get("attendance", 0)

            # Importa dialog da competitions_ui
            from .competitions_ui import MatchDetailsDialog

            dialog = MatchDetailsDialog(temp_match, match_data, parent=self)
            dialog.exec()

class UEFACompetitionInfoWidget(QWidget):
    """Widget info competizione UEFA"""

    def __init__(self, competition_name: str, parent=None):
        super().__init__(parent)
        self.competition_name = competition_name
        self.competition_manager = None
        self.setup_ui()

    def setup_ui(self):
        """Setup interfaccia"""
        layout = QVBoxLayout(self)

        # Titolo
        title_label = QLabel(self.competition_name)
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # Progress bar fase
        self.phase_progress = QProgressBar()
        self.phase_progress.setTextVisible(True)
        layout.addWidget(self.phase_progress)

        # Info competizione
        self.info_text = QTextEdit()
        self.info_text.setMaximumHeight(100)
        self.info_text.setReadOnly(True)
        layout.addWidget(self.info_text)

        # Nota: Simulazione gestita dal GameEngine centrale
        info_note = QLabel("ℹ️ Le partite vengono simulate automaticamente dal GameEngine")
        info_note.setStyleSheet("color: #666; font-style: italic; padding: 10px;")
        info_note.setWordWrap(True)
        layout.addWidget(info_note)

    def load_competition(self, competition_manager: UEFACompetitionManager):
        """Carica dati competizione"""
        self.competition_manager = competition_manager
        self.update_display()

    def update_display(self):
        """Aggiorna visualizzazione"""
        if not self.competition_manager:
            return

        stats = self.competition_manager.get_competition_stats()

        # Aggiorna progress bar
        current_md = stats.get("current_matchday", 0)
        max_md = stats.get("max_matchdays", 8)
        progress_percentage = int((current_md / max_md) * 100) if max_md > 0 else 0

        self.phase_progress.setValue(progress_percentage)
        self.phase_progress.setFormat(f"Giornata {current_md}/{max_md}")

        # Aggiorna info
        info_text = f"Squadre: {stats.get('teams_count', 0)}\n"
        info_text += f"Partite giocate: {stats.get('matches_played', 0)}\n"

        if stats.get("league_phase_completed", False):
            info_text += f"\n🏆 LEAGUE PHASE COMPLETATA!\n"
            info_text += f"Qualificate R16: {stats.get('qualified_r16', 0)}\n"
            info_text += f"Playoff: {stats.get('playoff_teams', 0)}\n"
            info_text += f"Eliminate: {stats.get('eliminated', 0)}"
        else:
            info_text += f"\n⏳ Prossima giornata: {current_md + 1}"

        self.info_text.setText(info_text)

    # Metodo simulate_next_matchday rimosso - simulazione gestita dal GameEngine

class EuropaUI(QWidget):
    """Interfaccia utente per competizioni europee"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.uefa_competitions = {}
        self.competition_tabs = {}
        self.setup_ui()

    def setup_ui(self):
        """Setup interfaccia"""
        layout = QVBoxLayout(self)

        # Titolo
        title_label = QLabel("⭐ COMPETIZIONI EUROPEE UEFA ⭐")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet(f"color: {COLOR_PRIMARY}; margin: 10px;")
        layout.addWidget(title_label)

        # Tab widget per le competizioni
        self.uefa_tab_widget = QTabWidget()
        layout.addWidget(self.uefa_tab_widget)

        # Info placeholder
        self.placeholder_label = QLabel("Le competizioni UEFA verranno caricate automaticamente\nse ci sono squadre qualificate.")
        self.placeholder_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.placeholder_label.setStyleSheet("color: gray; font-style: italic; margin: 50px;")
        layout.addWidget(self.placeholder_label)

    def load_uefa_competitions(self, uefa_competitions: Dict[str, UEFACompetitionManager]):
        """Carica competizioni UEFA"""
        self.uefa_competitions = uefa_competitions
        self.setup_uefa_tabs()

    def setup_uefa_tabs(self):
        """Setup tab per competizioni UEFA"""
        if self.uefa_competitions:
            self.placeholder_label.setVisible(False)
            self.uefa_tab_widget.setVisible(True)
        else:
            self.placeholder_label.setVisible(True)
            self.uefa_tab_widget.setVisible(False)
            return

        if not self.competition_tabs:
            # Crea i tab per la prima volta
            for comp_name, competition_manager in self.uefa_competitions.items():
                tab_widget = self.create_competition_tab(comp_name, competition_manager)
                self.competition_tabs[comp_name] = tab_widget
                tab_title = comp_name.replace("UEFA ", "")  # Accorcia il nome
                self.uefa_tab_widget.addTab(tab_widget, tab_title)
        else:
            # Aggiorna i tab esistenti
            for comp_name, competition_manager in self.uefa_competitions.items():
                if comp_name in self.competition_tabs:
                    self.update_competition_tab(self.competition_tabs[comp_name], competition_manager)

    def create_competition_tab(self, competition_name: str, competition_manager: UEFACompetitionManager) -> QWidget:
        """Crea tab per singola competizione"""
        tab_widget = QWidget()
        layout = QVBoxLayout(tab_widget)

        # Splitter per layout
        splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(splitter)

        # Panel sinistro: Info competizione
        info_widget = UEFACompetitionInfoWidget(competition_name)
        info_widget.load_competition(competition_manager)
        splitter.addWidget(info_widget)

        # Panel centrale: Classifica
        standings_group = QGroupBox("Classifica League Phase")
        standings_layout = QVBoxLayout(standings_group)

        standings_table = UEFAStandingsTableWidget()
        standings_layout.addWidget(standings_table)

        # Carica classifica
        standings = competition_manager.get_league_standings()
        standings_table.load_uefa_standings(standings)

        splitter.addWidget(standings_group)

        # Panel destro: Risultati recenti
        matches_group = QGroupBox("Risultati Recenti")
        matches_layout = QVBoxLayout(matches_group)

        matches_table = UEFAMatchesTableWidget()
        matches_layout.addWidget(matches_table)

        # Carica partite giocate dalla competizione
        played_matches_data = []
        if competition_manager:
            played_matches = competition_manager.get_played_matches()
            # Converti in formato per la tabella
            for match in played_matches:
                played_matches_data.append({
                    "matchday": match.matchday,
                    "home_team": match.home_team,
                    "away_team": match.away_team,
                    "home_score": match.home_score if match.played else "",
                    "away_score": match.away_score if match.played else "",
                    "competition": match.competition,
                    "attendance": match.attendance if match.played else 0
                })
        matches_table.load_uefa_matches(played_matches_data)

        splitter.addWidget(matches_group)

        # Imposta dimensioni splitter
        splitter.setSizes([250, 500, 300])

        return tab_widget

    def update_competition_tab(self, tab_widget: QWidget, competition_manager: UEFACompetitionManager):
        """Aggiorna il contenuto di un tab di competizione esistente"""
        # Trova i widget nel tab
        info_widget = tab_widget.findChild(UEFACompetitionInfoWidget)
        standings_table = tab_widget.findChild(UEFAStandingsTableWidget)
        matches_table = tab_widget.findChild(UEFAMatchesTableWidget)

        if info_widget:
            info_widget.load_competition(competition_manager)

        if standings_table:
            standings = competition_manager.get_league_standings()
            standings_table.load_uefa_standings(standings)

        if matches_table:
            played_matches_data = []
            if competition_manager:
                played_matches = competition_manager.get_played_matches()
                for match in played_matches:
                    played_matches_data.append({
                        "matchday": match.matchday,
                        "home_team": match.home_team,
                        "away_team": match.away_team,
                        "home_score": match.home_score if match.played else "",
                        "away_score": match.away_score if match.played else "",
                        "competition": match.competition,
                        "attendance": match.attendance if match.played else 0
                    })
            matches_table.load_uefa_matches(played_matches_data)

    def refresh_displays(self):
        """Aggiorna tutte le visualizzazioni"""
        self.setup_uefa_tabs()
        logger.info("Aggiornate visualizzazioni competizioni UEFA")