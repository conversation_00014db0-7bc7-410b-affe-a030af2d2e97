"""
Football Manager Italiano - Entry Point Principale
Gioco manageriale di calcio con PyQt6
"""
import sys
import os
from pathlib import Path

# Aggiunge src al path per import
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QPixmap, QFont, QIcon

from src.core.config import *
from src.core.utils import logger, setup_logging
from src.data.data_loader import data_loader
from src.data.name_generator import name_generator
from src.ui.main_window import MainWindow

class FootballManagerApp:
    """Classe principale dell'applicazione Football Manager"""

    def __init__(self):
        self.app = None
        self.main_window = None
        self.splash = None

    def initialize_application(self) -> bool:
        """Inizializza l'applicazione PyQt6"""
        try:
            # Crea applicazione Qt
            self.app = QApplication(sys.argv)
            self.app.setApplicationName(WINDOW_TITLE)
            self.app.setApplicationVersion("1.0")

            # Configura font dell'applicazione
            font = QFont(MAIN_FONT_FAMILY, MAIN_FONT_SIZE)
            self.app.setFont(font)

            # Configura stile applicazione (tema chiaro)
            self._setup_application_style()

            logger.info("Applicazione PyQt6 inizializzata")
            return True

        except Exception as e:
            logger.error(f"Errore inizializzazione applicazione: {e}")
            return False

    def _setup_application_style(self):
        """Configura lo stile dell'applicazione"""
        style = f"""
        QMainWindow {{
            background-color: {COLOR_BACKGROUND};
            color: {COLOR_TEXT};
        }}

        QMenuBar {{
            background-color: {COLOR_SURFACE};
            border-bottom: 1px solid {COLOR_BORDER};
            color: {COLOR_TEXT};
            font-size: {MAIN_FONT_SIZE}pt;
        }}

        QMenuBar::item {{
            background-color: transparent;
            padding: 6px 12px;
        }}

        QMenuBar::item:selected {{
            background-color: {COLOR_PRIMARY};
            color: white;
        }}

        QMenu {{
            background-color: {COLOR_BACKGROUND};
            border: 1px solid {COLOR_BORDER};
            color: {COLOR_TEXT};
        }}

        QMenu::item:selected {{
            background-color: {COLOR_PRIMARY};
            color: white;
        }}

        QToolBar {{
            background-color: {COLOR_SURFACE};
            border: 1px solid {COLOR_BORDER};
        }}

        QPushButton {{
            background-color: {COLOR_SURFACE};
            border: 1px solid {COLOR_BORDER};
            color: {COLOR_TEXT};
            padding: 8px 16px;
            font-size: {MAIN_FONT_SIZE}pt;
            border-radius: 4px;
        }}

        QPushButton:hover {{
            background-color: {COLOR_SECONDARY};
            color: white;
        }}

        QPushButton:pressed {{
            background-color: {COLOR_PRIMARY};
        }}

        QTableWidget {{
            background-color: {COLOR_BACKGROUND};
            alternate-background-color: {COLOR_SURFACE};
            gridline-color: {COLOR_BORDER};
            color: {COLOR_TEXT};
        }}

        QTableWidget::item:selected {{
            background-color: {COLOR_PRIMARY};
            color: white;
        }}

        QHeaderView::section {{
            background-color: {COLOR_SURFACE};
            border: 1px solid {COLOR_BORDER};
            color: {COLOR_TEXT};
            font-weight: bold;
            padding: 4px;
        }}

        QTabWidget::pane {{
            border: 1px solid {COLOR_BORDER};
            background-color: {COLOR_BACKGROUND};
        }}

        QTabBar::tab {{
            background-color: {COLOR_SURFACE};
            border: 1px solid {COLOR_BORDER};
            color: {COLOR_TEXT};
            padding: 8px 16px;
        }}

        QTabBar::tab:selected {{
            background-color: {COLOR_PRIMARY};
            color: white;
        }}

        QLabel {{
            color: {COLOR_TEXT};
        }}

        QLineEdit {{
            background-color: {COLOR_BACKGROUND};
            border: 1px solid {COLOR_BORDER};
            color: {COLOR_TEXT};
            padding: 4px;
        }}

        QComboBox {{
            background-color: {COLOR_BACKGROUND};
            border: 1px solid {COLOR_BORDER};
            color: {COLOR_TEXT};
            padding: 4px;
        }}
        """

        self.app.setStyleSheet(style)

    def show_splash_screen(self):
        """Mostra schermata di caricamento"""
        try:
            # Crea un pixmap semplice per la splash screen
            pixmap = QPixmap(400, 300)
            pixmap.fill(Qt.GlobalColor.white)

            self.splash = QSplashScreen(pixmap)
            self.splash.setWindowFlags(Qt.WindowType.WindowStaysOnTopHint | Qt.WindowType.SplashScreen)

            # Mostra messaggio sulla splash
            self.splash.showMessage("Caricamento Football Manager Italiano...",
                                  Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter,
                                  Qt.GlobalColor.black)
            self.splash.show()

            # Processa eventi per mostrare la splash
            self.app.processEvents()

        except Exception as e:
            logger.warning(f"Impossibile mostrare splash screen: {e}")

    def load_game_data(self) -> bool:
        """Carica tutti i dati di gioco"""
        try:
            if self.splash:
                self.splash.showMessage("Caricamento dati campionati...",
                                      Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter,
                                      Qt.GlobalColor.black)
                self.app.processEvents()

            # Carica dati
            if not data_loader.load_all_data():
                logger.error("Errore caricamento dati")
                return False

            if self.splash:
                self.splash.showMessage("Validazione dati...",
                                      Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter,
                                      Qt.GlobalColor.black)
                self.app.processEvents()

            # Valida dati
            if not data_loader.validate_data_integrity():
                logger.warning("Validazione dati completata con warning")

            # Valida generatore nomi
            if not name_generator.validate_names_availability():
                logger.error("Generatore nomi non funzionale")
                return False

            logger.info("Dati di gioco caricati con successo")
            return True

        except Exception as e:
            logger.error(f"Errore caricamento dati: {e}")
            return False

    def create_main_window(self) -> bool:
        """Crea la finestra principale"""
        try:
            if self.splash:
                self.splash.showMessage("Inizializzazione interfaccia...",
                                      Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter,
                                      Qt.GlobalColor.black)
                self.app.processEvents()

            # Crea finestra principale
            self.main_window = MainWindow()

            if self.splash:
                self.splash.showMessage("Pronto!",
                                      Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter,
                                      Qt.GlobalColor.black)
                self.app.processEvents()

            return True

        except Exception as e:
            logger.error(f"Errore creazione finestra principale: {e}")
            return False

    def show_error_dialog(self, title: str, message: str):
        """Mostra dialog di errore"""
        error_dialog = QMessageBox()
        error_dialog.setIcon(QMessageBox.Icon.Critical)
        error_dialog.setWindowTitle(title)
        error_dialog.setText(message)
        error_dialog.setStandardButtons(QMessageBox.StandardButton.Ok)
        error_dialog.exec()

    def run(self) -> int:
        """Esegue l'applicazione"""
        try:
            # Configura logging
            setup_logging()
            logger.info("=== AVVIO FOOTBALL MANAGER ITALIANO ===")

            # Crea directory necessarie
            ensure_directories()

            # Inizializza applicazione
            if not self.initialize_application():
                self.show_error_dialog("Errore Inizializzazione",
                                     "Impossibile inizializzare l'applicazione")
                return 1

            # Mostra splash screen
            self.show_splash_screen()

            # Carica dati di gioco
            if not self.load_game_data():
                if self.splash:
                    self.splash.close()
                self.show_error_dialog("Errore Caricamento Dati",
                                     "Impossibile caricare i dati di gioco.\n"
                                     "Controlla che i file JSON siano presenti.")
                return 1

            # Crea finestra principale
            if not self.create_main_window():
                if self.splash:
                    self.splash.close()
                self.show_error_dialog("Errore Interfaccia",
                                     "Impossibile creare l'interfaccia utente")
                return 1

            # Nascondi splash e mostra finestra principale
            if self.splash:
                # Timer per chiudere la splash dopo un momento
                QTimer.singleShot(1000, self._finish_loading)
            else:
                self.main_window.show()

            logger.info("Applicazione avviata con successo")

            # Avvia loop eventi
            return self.app.exec()

        except Exception as e:
            logger.error(f"Errore fatale: {e}")
            if self.app:
                self.show_error_dialog("Errore Fatale", str(e))
            return 1

        finally:
            logger.info("=== CHIUSURA FOOTBALL MANAGER ITALIANO ===")

    def _finish_loading(self):
        """Completa il caricamento e mostra la finestra principale"""
        if self.splash:
            self.splash.finish(self.main_window)

        self.main_window.show()

        # Log statistiche
        stats = data_loader.get_statistics()
        logger.info(f"Statistiche: {stats}")

def main():
    """Funzione main"""
    app = FootballManagerApp()
    return app.run()

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)