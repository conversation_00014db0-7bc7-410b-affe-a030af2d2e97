"""
Dialog per i dettagli delle partite
"""
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
    QDialogButtonBox, QTextEdit, QGroupBox
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont
from typing import Optional, Dict
from src.competitions.calendar import Match


class MatchDetailsDialog(QDialog):
    """Finestra dettagli partita"""

    def __init__(self, match: Match, match_result: Optional[Dict] = None, parent=None):
        super().__init__(parent)
        self.match = match
        self.match_result = match_result
        self.setup_ui()

    def setup_ui(self):
        self.setWindowTitle(f"Dettagli Partita: {self.match.home_team} vs {self.match.away_team}")
        self.setModal(True)
        self.resize(500, 400)

        layout = QVBoxLayout(self)

        # Titolo partita
        title = QLabel(f"{self.match.home_team} {self.match.result_string} {self.match.away_team}")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        layout.addWidget(title)

        # Info base
        info_layout = QHBoxLayout()
        info_layout.addWidget(QLabel(f"Data: {self.match.date.strftime('%d/%m/%Y')}"))
        info_layout.addWidget(QLabel(f"Ora: {self.match.time}"))
        if hasattr(self.match, 'attendance') and self.match.attendance:
            info_layout.addWidget(QLabel(f"Spettatori: {self.match.attendance:,}"))
        layout.addLayout(info_layout)

        # Competizione e giornata
        comp_layout = QHBoxLayout()
        comp_layout.addWidget(QLabel(f"Competizione: {self.match.competition}"))
        if hasattr(self.match, 'matchday'):
            comp_layout.addWidget(QLabel(f"Giornata: {self.match.matchday}"))
        layout.addLayout(comp_layout)

        # Dettagli partita se disponibili
        if self.match_result:
            self.add_match_details(layout)

        # Pulsanti
        buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok)
        buttons.accepted.connect(self.accept)
        layout.addWidget(buttons)

    def add_match_details(self, layout: QVBoxLayout):
        """Aggiunge dettagli della partita se disponibili"""
        details_group = QGroupBox("Dettagli Partita")
        details_layout = QVBoxLayout(details_group)

        details_text = QTextEdit()
        details_text.setReadOnly(True)
        details_text.setMaximumHeight(150)

        # Formatta dettagli
        details_content = ""
        if 'events' in self.match_result:
            details_content += "<b>Eventi:</b><br>"
            for event in self.match_result['events']:
                details_content += f"  {event}<br>"

        if 'statistics' in self.match_result:
            stats = self.match_result['statistics']
            details_content += "<br><b>Statistiche:</b><br>"
            for key, value in stats.items():
                details_content += f"  {key}: {value}<br>"

        details_text.setHtml(details_content)
        details_layout.addWidget(details_text)
        layout.addWidget(details_group)
