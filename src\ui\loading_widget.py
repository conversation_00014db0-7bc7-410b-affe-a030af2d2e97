"""
Loading widget per caricamento stagione e giocatori
Simile a Football Manager con progressione dettagliata
"""
import sys
import time
from typing import Callable, Optional, Dict, Any
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QProgressBar, QPushButton, QTextEdit, QApplication
)
from PyQt6.QtCore import QThread, pyqtSignal, QTimer, Qt
from PyQt6.QtGui import QFont, QPixmap
from pathlib import Path

from ..core.config import *
from ..core.utils import logger
from ..data.data_loader import data_loader
from ..competitions.season import Season
from ..competitions.competition import setup_italian_competitions
from ..competitions.calendar import generate_full_season_calendar
from ..competitions.uefa_competitions import setup_uefa_competitions
from ..players.player_generator import player_generator

class SeasonLoadingWorker(QThread):
    """Worker thread per caricamento stagione in background"""

    # Segnali per comunicare progresso
    progress_updated = pyqtSignal(int, str)  # percentuale, messaggio
    task_completed = pyqtSignal(str)  # nome task
    loading_completed = pyqtSignal(dict)  # dati caricati
    loading_failed = pyqtSignal(str)  # messaggio errore

    def __init__(self):
        super().__init__()
        self.season = None
        self.competition_manager = None
        self.calendar = None
        self.uefa_competitions = {}
        self.teams_data = {}
        self.uefa_teams_data = {}

        # Carica dati reputazione nazioni per team_level dinamico
        self.nation_reputation_map = self._load_nation_reputation_data()

    def run(self):
        """Esegue il caricamento completo"""
        try:
            # Task 1: Caricamento dati base (10%)
            self.progress_updated.emit(5, "Inizializzazione dati...")
            time.sleep(0.5)

            if not data_loader.is_loaded():
                self.progress_updated.emit(10, "Caricamento nomi e campionati...")
                if not data_loader.load_all_data():
                    self.loading_failed.emit("Impossibile caricare i dati base")
                    return

            self.task_completed.emit("Dati base caricati")

            # Task 2: Creazione stagione (20%)
            self.progress_updated.emit(15, "Creazione stagione 2025/2026...")
            time.sleep(0.3)

            self.season = Season(2025)
            self.task_completed.emit("Stagione creata")

            # Task 3: Setup competizioni (40%)
            self.progress_updated.emit(25, "Setup campionati italiani...")
            time.sleep(0.4)

            # Prepara squadre per livello
            teams_by_level = {
                1: [team["nome"] for team in data_loader.get_serie_a_teams()],
                2: [team["nome"] for team in data_loader.get_serie_b_teams()]
            }

            # Ottieni gironi Serie C
            serie_c_groups = data_loader.get_serie_c_groups()

            self.progress_updated.emit(30, "Creazione competizioni italiane...")
            self.competition_manager = setup_italian_competitions(
                self.season, teams_by_level, serie_c_groups
            )
            self.task_completed.emit(f"Competizioni italiane create: {len(self.competition_manager.competitions)}")

            # Setup competizioni UEFA (senza giocatori per ora)
            self.progress_updated.emit(35, "Creazione competizioni UEFA...")
            self.uefa_competitions = setup_uefa_competitions(self.season)
            self.task_completed.emit(f"Competizioni UEFA create: {len(self.uefa_competitions)}")

            # Aggiungi competizioni UEFA al manager
            for comp in self.uefa_competitions.values():
                self.competition_manager.add_competition(comp)

            # Task 4: Generazione calendario (60%)
            self.progress_updated.emit(45, "Generazione calendario partite...")
            time.sleep(0.5)

            self.calendar = generate_full_season_calendar(self.season, self.competition_manager)

            self.progress_updated.emit(55, "Programmazione 1140+ partite...")
            time.sleep(0.3)

            summary = self.calendar.get_calendar_summary()
            self.task_completed.emit(f"Calendario generato: {summary['total_matches']} partite")

            # Task 5: Generazione giocatori (100%)
            processed_teams = set()

            self.progress_updated.emit(65, "Generazione giocatori per Serie A...")
            self._generate_players_for_level(teams_by_level[1], 1, 65, 75, processed_teams)

            self.progress_updated.emit(75, "Generazione giocatori per Serie B...")
            self._generate_players_for_level(teams_by_level[2], 2, 75, 85, processed_teams)

            self.progress_updated.emit(85, "Generazione giocatori per Serie C...")
            all_serie_c_teams = []
            for teams in serie_c_groups.values():
                all_serie_c_teams.extend(teams)
            self._generate_players_for_level(all_serie_c_teams, 3, 85, 95, processed_teams)

            self.task_completed.emit(f"Giocatori italiani generati per {len(processed_teams)} squadre")

            # Task 6: Setup competizioni UEFA e giocatori
            # Genera giocatori per squadre UEFA non ancora processate
            self.progress_updated.emit(97, "Generazione giocatori per squadre UEFA...")
            uefa_teams_to_generate = []
            for comp in self.uefa_competitions.values():
                for team_name in comp.teams:
                    if team_name not in processed_teams:
                        uefa_teams_to_generate.append(team_name)

            # Rimuovi duplicati
            uefa_teams_to_generate = list(set(uefa_teams_to_generate))

            if uefa_teams_to_generate:
                progress_step = (100 - 97) / len(uefa_teams_to_generate)
                for i, team_name in enumerate(uefa_teams_to_generate):
                    # Trova paese della squadra per determinare team_level realistico
                    team_country = self._get_team_country(team_name)
                    dynamic_team_level = self._get_team_level_from_country(team_country)

                    squad = player_generator.generate_team_squad(team_level=dynamic_team_level, squad_size=25)
                    for player in squad:
                        player.current_club = team_name

                    self.uefa_teams_data[team_name] = squad
                    processed_teams.add(team_name)

                    # Log per debug del bilanciamento
                    logger.debug(f"Squadra UEFA: {team_name} ({team_country}) -> team_level={dynamic_team_level}")

                    self.progress_updated.emit(97 + int((i + 1) * progress_step), f"Generati giocatori per {team_name} (Liv.{dynamic_team_level})...")

            self.task_completed.emit(f"Giocatori generati per {len(processed_teams)} squadre totali con bilanciamento realistico")

            # Imposta dati UEFA nelle competizioni
            if self.uefa_teams_data:
                for comp in self.uefa_competitions.values():
                    comp.set_uefa_teams_data(self.uefa_teams_data)
                self.task_completed.emit(f"Dati UEFA impostati per {len(self.uefa_competitions)} competizioni")

            # Imposta tutti i dati squadre nel competition manager
            all_teams_data = {**self.teams_data, **self.uefa_teams_data}
            self.competition_manager.set_teams_data_all(all_teams_data)
            self.task_completed.emit(f"Dati squadre impostati per tutte le competizioni")

            # Completamento
            self.progress_updated.emit(100, "Caricamento completato!")
            time.sleep(0.5)

            # Ritorna risultati
            result_data = {
                'season': self.season,
                'competition_manager': self.competition_manager,
                'calendar': self.calendar,
                'uefa_competitions': self.uefa_competitions,
                'teams_data': self.teams_data,
                'uefa_teams_data': self.uefa_teams_data
            }

            self.loading_completed.emit(result_data)

        except Exception as e:
            logger.error(f"Errore durante caricamento: {e}")
            import traceback
            traceback.print_exc()
            self.loading_failed.emit(f"Errore: {str(e)}")

    def _generate_players_for_level(self, teams: list, level: int, start_progress: int, end_progress: int, processed_teams: set):
        """Genera giocatori per un livello di campionato"""
        if not teams: 
            return

        progress_step = (end_progress - start_progress) / len(teams)

        for i, team_name in enumerate(teams):
            # Genera rosa
            squad = player_generator.generate_team_squad(team_level=level, squad_size=25)

            # Assegna squadra ai giocatori
            for player in squad:
                player.current_club = team_name

            self.teams_data[team_name] = squad
            processed_teams.add(team_name)

            # Aggiorna progresso
            current_progress = start_progress + int((i + 1) * progress_step)
            self.progress_updated.emit(current_progress, f"Generati giocatori per {team_name}...")

            time.sleep(0.1)  # Piccola pausa per mostrare progresso

    def _load_nation_reputation_data(self) -> Dict[str, int]:
        """Carica mappa reputazione nazioni da fifa_confederations.json"""
        import json
        from pathlib import Path

        reputation_map = {}

        try:
            fifa_data_path = Path(__file__).parent.parent.parent / "fifa_confederations.json"

            if fifa_data_path.exists():
                with open(fifa_data_path, 'r', encoding='utf-8') as f:
                    fifa_data = json.load(f)

                # Estrai reputazione per ogni paese
                for confederation_data in fifa_data.values():
                    for country in confederation_data.get("countries", []):
                        country_name = country.get("nome", "")
                        reputation = country.get("reputazione", 10)  # Default medio
                        reputation_map[country_name] = reputation

                logger.info(f"Caricate reputazioni per {len(reputation_map)} nazioni")
            else:
                logger.warning("File fifa_confederations.json non trovato, userò reputazioni di default")

        except Exception as e:
            logger.error(f"Errore caricamento reputazioni nazioni: {e}")

        return reputation_map

    def _get_team_level_from_country(self, country: str) -> int:
        """Determina team_level basandosi sulla reputazione della nazione"""
        if not country or country == "Unknown":
            return 2  # Default medio per squadre senza paese

        reputation = self.nation_reputation_map.get(country, 10)  # Default 10 se paese non trovato

        # Mapping reputazione -> team_level (più alto = più forte)
        # Reputazione FIFA va da 1-20, team_level va da 1-5 (1=migliore, 5=peggiore)
        if reputation >= 18:     # Top tier (Inghilterra, Italia, Spagna, Germania, Francia)
            return 1
        elif reputation >= 15:   # High tier (Olanda, Portogallo, Croazia, Belgio)
            return 1
        elif reputation >= 12:   # Medium-high tier (Turchia, Russia, Danimarca, Serbia, Polonia, Svezia)
            return 2
        elif reputation >= 9:    # Medium tier (Scozia, Svizzera, Austria, Grecia, Bosnia, Irlanda)
            return 2
        elif reputation >= 6:    # Medium-low tier (Bulgaria, Georgia, Albania, Montenegro, Bielorussia)
            return 3
        elif reputation >= 4:    # Low tier (Lettonia, Moldova, Armenia, Lituania, Estonia)
            return 4
        else:                    # Bottom tier (Malta, Liechtenstein, Gibilterra, Andorra, San Marino)
            return 5

    def _get_team_country(self, team_name: str) -> str:
        """Determina paese di una squadra UEFA dalle qualificazioni"""
        # Cerca nei dati delle qualificate UEFA per trovare il paese della squadra
        if hasattr(self, 'uefa_competitions') and self.uefa_competitions:
            for comp_name, competition in self.uefa_competitions.items():
                for team in competition.teams:
                    if team == team_name:
                        # Cerca nei dati qualificati originali
                        qualified_teams = data_loader.get_uefa_qualified_teams()
                        for comp_teams in qualified_teams.values():
                            for team_data in comp_teams:
                                if team_data.get("nome") == team_name:
                                    return team_data.get("country", "Unknown")

        # Se non trovato, prova a mappare manualmente squadre più famose
        famous_teams_country_map = {
            # Inghilterra
            'Manchester City': 'Inghilterra', 'Arsenal': 'Inghilterra', 'Liverpool': 'Inghilterra',
            'Chelsea': 'Inghilterra', 'Manchester United': 'Inghilterra', 'Tottenham': 'Inghilterra',
            'Newcastle': 'Inghilterra', 'Brighton': 'Inghilterra', 'Aston Villa': 'Inghilterra',

            # Spagna
            'Real Madrid': 'Spagna', 'Barcelona': 'Spagna', 'Atletico Madrid': 'Spagna',
            'Sevilla': 'Spagna', 'Villarreal': 'Spagna', 'Real Betis': 'Spagna', 'Valencia': 'Spagna',

            # Germania
            'Bayern Munich': 'Germania', 'Borussia Dortmund': 'Germania', 'RB Leipzig': 'Germania',
            'Bayer Leverkusen': 'Germania', 'Eintracht Frankfurt': 'Germania',

            # Francia
            'Paris Saint-Germain': 'Francia', 'Marseille': 'Francia', 'Monaco': 'Francia',
            'Lyon': 'Francia', 'Lille': 'Francia', 'Rennes': 'Francia',

            # Paesi Bassi
            'Ajax': 'Paesi Bassi', 'PSV': 'Paesi Bassi', 'Feyenoord': 'Paesi Bassi',

            # Portogallo
            'Porto': 'Portogallo', 'Benfica': 'Portogallo', 'Sporting CP': 'Portogallo',

            # Altri paesi europei
            'Club Brugge': 'Belgio', 'Anderlecht': 'Belgio',
            'Red Bull Salzburg': 'Austria', 'Sturm Graz': 'Austria',
        }

        return famous_teams_country_map.get(team_name, "Unknown")

class LoadingWidget(QWidget):
    """Widget di caricamento stile Football Manager"""

    # Segnali
    loading_completed = pyqtSignal(dict)  # dati caricati
    loading_cancelled = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.worker = None
        self.setup_ui()

    def setup_ui(self):
        """Setup interfaccia"""
        self.setWindowTitle("Football Manager Italiano - Caricamento")
        self.setFixedSize(600, 400)
        self.setStyleSheet(f"background-color: {COLOR_BACKGROUND};")

        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(40, 40, 40, 40)

        # Titolo
        title = QLabel("Football Manager Italiano")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet(f"""
            font-size: 24px;
            font-weight: bold;
            color: {COLOR_PRIMARY};
            margin-bottom: 10px;
        """)
        layout.addWidget(title)

        # Sottotitolo
        subtitle = QLabel("Preparazione stagione 2025/2026...")
        subtitle.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle.setStyleSheet(f"""
            font-size: 14px;
            color: {COLOR_TEXT_SECONDARY};
            margin-bottom: 20px;
        """)
        layout.addWidget(subtitle)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 2px solid {COLOR_BORDER};
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                background-color: {COLOR_SURFACE};
                height: 30px;
            }}
            QProgressBar::chunk {{
                background-color: {COLOR_SUCCESS};
                border-radius: 6px;
            }}
        """)
        layout.addWidget(self.progress_bar)

        # Messaggio status
        self.status_label = QLabel("Pronto per il caricamento...")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setStyleSheet(f"""
            font-size: 12px;
            color: {COLOR_TEXT_SECONDARY};
            margin-top: 10px;
        """)
        layout.addWidget(self.status_label)

        # Log area
        self.log_area = QTextEdit()
        self.log_area.setMaximumHeight(120)
        self.log_area.setStyleSheet(f"""
            QTextEdit {{
                background-color: {COLOR_SURFACE};
                border: 1px solid {COLOR_BORDER};
                border-radius: 6px;
                font-family: 'Consolas', monospace;
                font-size: 10px;
                color: {COLOR_TEXT};
                padding: 10px;
            }}
        """)
        self.log_area.setReadOnly(True)
        layout.addWidget(self.log_area)

        # Bottoni
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        self.start_button = QPushButton("Inizia Nuova Stagione")
        self.start_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLOR_SUCCESS};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: #27AE60;
            }}
            QPushButton:disabled {{
                background-color: {COLOR_BORDER};
                color: {COLOR_TEXT_SECONDARY};
            }}
        """)
        self.start_button.clicked.connect(self.start_loading)
        button_layout.addWidget(self.start_button)

        self.cancel_button = QPushButton("Annulla")
        self.cancel_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLOR_SURFACE};
                color: {COLOR_TEXT};
                border: 2px solid {COLOR_BORDER};
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: {COLOR_BORDER};
            }}
        """)
        self.cancel_button.clicked.connect(self.cancel_loading)
        button_layout.addWidget(self.cancel_button)

        layout.addLayout(button_layout)

    def start_loading(self):
        """Inizia il caricamento"""
        self.start_button.setEnabled(False)
        self.progress_bar.setValue(0)
        self.status_label.setText("Caricamento in corso...")
        self.log_area.clear()

        # Crea e avvia worker
        self.worker = SeasonLoadingWorker()
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.task_completed.connect(self.add_log)
        self.worker.loading_completed.connect(self.on_loading_completed)
        self.worker.loading_failed.connect(self.on_loading_failed)

        self.worker.start()

    def cancel_loading(self):
        """Annulla il caricamento"""
        if self.worker and self.worker.isRunning():
            self.worker.terminate()
            self.worker.wait()

        self.loading_cancelled.emit()
        self.close()

    def update_progress(self, value: int, message: str):
        """Aggiorna progress bar e messaggio"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)

    def add_log(self, message: str):
        """Aggiunge messaggio al log"""
        self.log_area.append(f"✓ {message}")
        # Scroll to bottom
        scrollbar = self.log_area.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def on_loading_completed(self, data: dict):
        """Gestisce completamento caricamento"""
        self.add_log("CARICAMENTO COMPLETATO!")
        self.status_label.setText("Stagione pronta! Seleziona la tua squadra.")

        # Piccolo delay prima di emettere segnale
        QTimer.singleShot(1000, lambda: self.loading_completed.emit(data))

    def on_loading_failed(self, error_message: str):
        """Gestisce errore nel caricamento"""
        self.add_log(f"ERRORE: {error_message}")
        self.status_label.setText("Caricamento fallito.")
        self.start_button.setEnabled(True)
        self.start_button.setText("Riprova")

    def closeEvent(self, event):
        """Override close event per terminare worker"""
        if self.worker and self.worker.isRunning():
            self.worker.terminate()
            self.worker.wait()
        super().closeEvent(event)

# Test widget per sviluppo
if __name__ == "__main__":
    app = QApplication(sys.argv)

    # Test del widget
    loading_widget = LoadingWidget()

    def on_completed(data):
        print(f"Caricamento completato! Dati: {list(data.keys())}")
        app.quit()

    def on_cancelled():
        print("Caricamento annullato")
        app.quit()

    loading_widget.loading_completed.connect(on_completed)
    loading_widget.loading_cancelled.connect(on_cancelled)

    loading_widget.show()

    sys.exit(app.exec())