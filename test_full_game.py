"""
Test completo del gioco Football Manager Italiano
"""
import sys
from pathlib import Path

src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

from src.core.config import ensure_directories
from src.data.data_loader import data_loader
from src.ui.main_window import MainWindow

def test_full_application():
    """Test completo dell'applicazione"""
    print("=== TEST COMPLETO FOOTBALL MANAGER ITALIANO ===")

    app = QApplication(sys.argv)

    # Carica dati
    ensure_directories()
    print("Caricamento dati in corso...")
    if not data_loader.load_all_data():
        print("ERRORE: Impossibile caricare i dati")
        return False

    print("Dati caricati con successo")

    # Statistiche caricate
    stats = data_loader.get_statistics()
    print(f"Statistiche:")
    print(f"   - <PERSON><PERSON>: {stats.get('nazioni_nomi', 0)}")
    print(f"   - Squadre italiane: {stats.get('squadre_italiane', 0)}")
    print(f"   - Serie A: {stats.get('squadre_serie_a', 0)} squadre")
    print(f"   - Serie B: {stats.get('squadre_serie_b', 0)} squadre")
    print(f"   - Serie C: {stats.get('squadre_serie_c', 0)} squadre")

    # Crea finestra principale
    print("\nInizializzazione interfaccia...")
    main_window = MainWindow()

    # Mostra finestra
    main_window.show()
    main_window.resize(1400, 900)

    print("Applicazione avviata con successo!")
    print("\nCONTROLLI DISPONIBILI:")
    print("   1. Seleziona una squadra dal dialog iniziale")
    print("   2. Tab 'Squadra': Visualizza i giocatori della tua squadra")
    print("   3. Tab 'Competizioni': Visualizza campionati e classifiche")
    print("   4. Usa i filtri e visualizza dettagli giocatori")
    print("   5. Simula giornate di campionato")

    print(f"\nTest automatico in corso per 10 secondi...")

    # Chiudi automaticamente dopo 10 secondi per test
    def close_app():
        print("\nTEST COMPLETATO - Applicazione funzionante!")
        print("STATO: Il gioco è completamente GIOCABILE")
        main_window.close()
        app.quit()

    QTimer.singleShot(10000, close_app)

    app.exec()
    return True

if __name__ == "__main__":
    try:
        if test_full_application():
            print("\nRISULTATO: SUCCESS")
            print("Football Manager Italiano è pronto per essere giocato!")
        else:
            print("\nRISULTATO: FAILED")
            sys.exit(1)
    except Exception as e:
        print(f"\nERRORE CRITICO: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)