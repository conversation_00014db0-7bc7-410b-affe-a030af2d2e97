# Football Manager Italiano

Un gioco manageriale di calcio sviluppato in Python con PyQt6, dove puoi dirigere una squadra italiana dai 3 livelli del calcio professionistico.

## 🎯 Caratteristiche Principali

- **🏆 Sistema Competizioni Completo**: Serie A, Serie B, Serie C (3 gironi) con classifiche e calendari realistici
- **🏅 Coppe Nazionali**: Coppa Italia, Supercoppa Italiana, Coppa Italia Serie C con eliminazioni dirette
- **👨‍💼 Gestione Dirigenziale**: Focus su decisioni strategiche, finanze e sviluppo club
- **⚽ Sistema Giocatori Avanzato**: 110 nazioni, attributi realistici, sviluppo e statistiche
- **📅 Stagioni Calcistiche**: Sistema temporale completo con fasi stagionali e mercato
- **🎮 Interfaccia Professionale**: <PERSON><PERSON> chiaro, design stile Football Manager con loading widget
- **📊 Simulazione Realistica**: 1140+ partite per stagione con risultati credibili

## 🚀 Funzionalità Implementate

### ✅ **Sistema Base** (Fase 1)
- Struttura progetto modulare (300 righe max per file)
- Caricamento dati JSON per 110 nazioni
- Interfaccia PyQt6 con tema chiaro professionale
- Dialog selezione squadra iniziale
- Sistema logging e configurazione

### ✅ **Sistema Giocatori** (Fase 2)
- **Generazione giocatori**: Nomi dalle 110 nazioni, attributi per posizione
- **Gestione squadra**: Interfaccia con tabella giocatori, filtri, dettagli
- **Sistema attributi**: 10 attributi (tecnica, velocità, forza, etc.) scala 1-20
- **Sviluppo giocatori**: Allenamento, potenziale, declino età
- **Statistiche**: Presenze, gol, assist, voti partita

### ✅ **Sistema Stagioni** (Fase 3)
- **Stagioni complete**: Luglio-Giugno con fasi realistiche
- **Calendari automatici**: 1140 partite generate con date/orari realistici
- **Competizioni**: Serie A/B/C con classifiche automatiche
- **Simulazione partite**: Match engine con eventi e statistiche
- **Interfaccia competizioni**: Classifiche colorate, calendario, controlli

### ✅ **Sistema Coppe Nazionali** (Fase 4)
- **Coppa Italia**: Formato realistico con 7 fasi eliminatorie, 44 squadre partecipanti
- **Supercoppa Italiana**: Final Four con vincitori Serie A/Coppa Italia (Napoli, Inter, Bologna, Atalanta)
- **Coppa Italia Serie C**: Competizione dedicata per squadre di terza divisione (56 squadre)
- **Simulazione eliminazioni**: Partite secche e andata/ritorno con rigori
- **Interface coppe**: Tab dedicato con stato competizioni e vincitori

## 📁 Struttura del Progetto

```
Football Manager Italiano/
├── main.py                          # Entry point principale
├── src/                             # Codice sorgente
│   ├── core/                        # Sistema base
│   │   ├── config.py               # Configurazioni e costanti
│   │   └── utils.py                # Funzioni utility comuni
│   ├── data/                        # Gestione dati
│   │   ├── data_loader.py          # Caricamento JSON campionati
│   │   └── name_generator.py       # Generazione nomi da 110 nazioni
│   ├── players/                     # Sistema giocatori
│   │   ├── player.py               # Modello giocatore completo
│   │   ├── player_generator.py     # Generazione giocatori
│   │   └── player_stats.py         # Statistiche e sviluppo
│   ├── competitions/                # Sistema competizioni
│   │   ├── season.py               # Gestione stagioni
│   │   ├── calendar.py             # Calendari partite
│   │   ├── competition.py          # Campionati e classifiche
│   │   ├── cup_competitions.py     # Coppe nazionali (Coppa Italia, Supercoppa, Serie C)
│   │   └── match_engine.py         # Simulazione partite
│   ├── ui/                          # Interfaccia grafica
│   │   ├── main_window.py          # Finestra principale
│   │   ├── squad_ui.py             # Interfaccia squadra
│   │   └── competitions_ui.py      # Interfaccia competizioni
│   └── club/                        # Gestione club (futuro)
├── data/                            # Directory dati
└── assets/                          # Risorse grafiche
```

## 🛠️ Installazione e Avvio

### Prerequisiti
- **Python 3.8+**
- **PyQt6**

### Installazione
```bash
pip install PyQt6
```

### Avvio del Gioco
```bash
python main.py
```

### Test delle Funzionalità
```bash
# Test sistema base
python test_app.py

# Test sistema giocatori
python test_players.py

# Test sistema competizioni
python test_competitions.py
```

## 🎮 Come Giocare

1. **Avvia l'applicazione**: `python main.py`
2. **Selezione squadra**: Scegli tra Serie A, B o C
3. **Gestisci il tuo club**:
   - **Squadra**: Visualizza giocatori, attributi, statistiche
   - **Competizioni**: Segui classifiche, calendario, simula giornate
   - **Panoramica**: Info generali club

### Controlli Principali
- **Tab Squadra**: Gestione rosa, dettagli giocatori, genera squadra test
- **Tab Competizioni**: Classifiche Serie A/B/C, calendario partite, simula giornate
- **Bottone "Genera Stagione Test"**: Crea stagione completa con dati realistici
- **Filtri squadra**: Visualizza partite specifiche per squadra

## 📊 Dati del Gioco

- **110 nazioni** con nomi e cognomi autentici
- **100 squadre italiane** dai 3 livelli ufficiali (Serie A: 20, Serie B: 20, Serie C: 60)
- **8 competizioni**: 5 campionati (Serie A, B, C-A, C-B, C-C) + 3 coppe nazionali
- **1140+ partite** per stagione completa nei campionati
- **100+ partite** aggiuntive nelle coppe (Coppa Italia, Supercoppa, Serie C)
- **25 giocatori** per squadra con attributi realistici
- **38 giornate** per campionato (andata e ritorno)

## 🔧 Architettura Tecnica

- **Linguaggio**: Python 3.8+ con type hints
- **GUI**: PyQt6 con tema chiaro professionale
- **Dati**: File JSON per flessibilità e performance
- **Architettura**: Modulare, max 300 righe per file
- **Logging**: Sistema completo per debug
- **Pattern**: MVC con separazione logica/interfaccia

## 📈 Log di Sviluppo

### ✅ **Fase 1 - Sistema Base**
- [x] Struttura progetto modulare
- [x] Sistema configurazione e logging
- [x] Caricamento dati JSON (110 nazioni, campionati)
- [x] Interfaccia PyQt6 con tema chiaro
- [x] Dialog selezione squadra

### ✅ **Fase 2 - Sistema Giocatori**
- [x] Modello Player con attributi completi
- [x] Generazione giocatori dalle 110 nazioni
- [x] Sistema allenamento e sviluppo
- [x] Interfaccia gestione squadra
- [x] Statistiche e valore mercato

### ✅ **Fase 3 - Sistema Stagioni**
- [x] Modello Season con fasi temporali
- [x] Generazione calendario automatico
- [x] Sistema competizioni e classifiche
- [x] Match engine con simulazione realistica
- [x] Interfaccia competizioni completa

## 🎯 Prossimi Sviluppi

### **Fase 4 - Sistema Trasferimenti**
- [ ] Mercato estivo/invernale
- [ ] Negoziazioni e offerte
- [ ] Prestiti e clausole
- [ ] Budget dinamico

### **Fase 5 - Sistema Finanziario**
- [ ] Bilanci dettagliati
- [ ] Ricavi stadio/sponsor
- [ ] Costi operativi
- [ ] Fair Play Finanziario

### **Fase 6 - Gestione Avanzata**
- [ ] Staff tecnico (allenatore autonomo)
- [ ] Settore giovanile
- [ ] Strutture club
- [ ] Obiettivi stagionali

### **Fase 7 - Espansione**
- [ ] Coppe nazionali/europee
- [ ] Sistema reputazione
- [ ] Media e tifosi
- [ ] Modalità carriera

## 📝 Test e Qualità

- **Copertura test**: Tutti i moduli principali
- **Performance**: Generazione 1140 partite in <1 secondo
- **Usabilità**: Interfaccia intuitiva e responsive
- **Stabilità**: Gestione errori e validazione dati

## 🏆 Status Attuale

**🎮 GIOCABILE** - Il gioco ha tutte le funzionalità base per un'esperienza completa:
- Caricamento automatico con loading widget professionale
- Selezione e gestione squadra con 2500+ giocatori generati
- 5 campionati italiani funzionanti (Serie A/B/C con 3 gironi)
- 3 coppe nazionali (Coppa Italia, Supercoppa, Coppa Italia Serie C)
- Simulazione stagioni complete con 1200+ partite totali
- Classifiche e risultati realistici con promozioni/retrocessioni

---

**Football Manager Italiano v0.4** - *Sistema coppe nazionali e loading widget aggiunti!*

*Un'esperienza manageriale autentica nel calcio italiano con oltre 1200 partite simulate per stagione tra campionati e coppe nazionali.*