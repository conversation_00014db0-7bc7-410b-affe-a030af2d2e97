"""
Interfaccia principale competizioni - Versione modulare
"""
from PyQt6.QtWidgets import (
    QWidget, Q<PERSON>oxLayout, QHBoxLayout, QComboBox,
    QGroupBox, QSplitter, QTextEdit, QFrame, QLabel, QStackedWidget
)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QFont
from typing import Optional

from src.competitions.season import Season
from src.competitions.competition import CompetitionManager, LeagueCompetition
from src.competitions.calendar import MatchCalendar
from src.core.config import COLOR_PRIMARY, HEADER_FONT_SIZE
from ..calendar_widget import MatchCalendarWidget

from .widgets.season_info import SeasonInfoWidget
from .tabs.competition_tab import CompetitionTabWidget
from .tabs.cups_tab import CupsTabWidget


class CompetitionsUI(QWidget):
    """Interfaccia principale competizioni - Riprogettata modularmente"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.season: Optional[Season] = None
        self.competition_manager: Optional[CompetitionManager] = None
        self.calendar: Optional[MatchCalendar] = None
        
        # Competition widgets
        self.serie_a_tab: Optional[CompetitionTabWidget] = None
        self.serie_b_tab: Optional[CompetitionTabWidget] = None
        self.serie_c_tabs: dict = {}
        self.cups_tab: Optional[CupsTabWidget] = None

        # UI components
        self.competition_selector: Optional[QComboBox] = None
        self.competition_stack: Optional[QStackedWidget] = None
        
        # Altri widget
        self.season_info: Optional[SeasonInfoWidget] = None
        self.calendar_widget: Optional[MatchCalendarWidget] = None
        self.summary_text: Optional[QTextEdit] = None
        
        self.setup_ui()

        # Timer per aggiornamenti automatici
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.refresh_displays)
        self.update_timer.start(30000)  # Aggiorna ogni 30 secondi

    def setup_ui(self):
        """Configura interfaccia - Riprogettata con layout migliore"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # Header con titolo
        self.create_header(layout)

        # Splitter principale - Organizzato in sezioni più chiare
        main_splitter = QSplitter(Qt.Orientation.Vertical)
        main_splitter.setHandleWidth(8)

        # Panel superiore - Informazioni di stagione e calendario
        self.create_top_panel(main_splitter)

        # Panel centrale - Competizioni con menu a tendina
        self.create_competitions_panel(main_splitter)

        # Panel inferiore - Riepilogo
        self.create_summary_panel(main_splitter)

        # Proporzioni splitter
        main_splitter.setSizes([200, 600, 150])
        
        layout.addWidget(main_splitter)

    def create_header(self, layout: QVBoxLayout):
        """Crea header con titolo"""
        header_layout = QHBoxLayout()
        
        title_label = QLabel("⚽ GESTIONE COMPETIZIONI ⚽")
        title_font = QFont("Arial", HEADER_FONT_SIZE + 4, QFont.Weight.Bold)
        title_label.setFont(title_font)
        title_label.setStyleSheet(f"color: {COLOR_PRIMARY}; margin: 10px;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(title_label)
        
        layout.addLayout(header_layout)

    def create_top_panel(self, main_splitter: QSplitter):
        """Crea panel superiore con info stagione e calendario"""
        top_panel = QFrame()
        top_panel.setFrameStyle(QFrame.Shape.StyledPanel)
        top_layout = QHBoxLayout(top_panel)
        top_layout.setSpacing(10)

        # Informazioni stagione
        self.season_info = SeasonInfoWidget()
        top_layout.addWidget(self.season_info)
        
        # Calendario partite
        self.calendar_widget = MatchCalendarWidget()
        top_layout.addWidget(self.calendar_widget)

        main_splitter.addWidget(top_panel)

    def create_competitions_panel(self, main_splitter: QSplitter):
        """Crea panel competizioni con menu a tendina"""
        competitions_panel = QFrame()
        competitions_panel.setFrameStyle(QFrame.Shape.StyledPanel)
        panel_layout = QVBoxLayout(competitions_panel)
        panel_layout.setSpacing(10)

        # Header con menu a tendina
        header_layout = QHBoxLayout()

        # Label
        comp_label = QLabel("🏆 Seleziona Competizione:")
        comp_label.setStyleSheet(f"color: {COLOR_PRIMARY}; font-weight: bold; font-size: 14px;")
        header_layout.addWidget(comp_label)

        # Menu a tendina
        self.competition_selector = QComboBox()
        self.competition_selector.setMinimumWidth(250)
        self.competition_selector.setStyleSheet("""
            QComboBox {
                padding: 8px;
                font-size: 13px;
                border: 2px solid #ddd;
                border-radius: 5px;
                background-color: white;
            }
            QComboBox:hover {
                border-color: """ + COLOR_PRIMARY + """;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #666;
                margin-right: 10px;
            }
        """)

        # Popola menu a tendina
        competitions = [
            ("🏆 Serie A", "serie_a"),
            ("🥈 Serie B", "serie_b"),
            ("🥉 Serie C - Girone A", "serie_c_a"),
            ("🥉 Serie C - Girone B", "serie_c_b"),
            ("🥉 Serie C - Girone C", "serie_c_c"),
            ("🏆 Coppe Nazionali", "cups")
        ]

        for display_name, key in competitions:
            self.competition_selector.addItem(display_name, key)

        self.competition_selector.currentTextChanged.connect(self.on_competition_changed)
        header_layout.addWidget(self.competition_selector)
        header_layout.addStretch()

        panel_layout.addLayout(header_layout)

        # Stack widget per contenuti
        self.competition_stack = QStackedWidget()

        # Crea widget per ogni competizione
        self.create_competition_widgets()

        panel_layout.addWidget(self.competition_stack)
        main_splitter.addWidget(competitions_panel)

    def create_competition_widgets(self):
        """Crea widget per ogni competizione"""
        # Serie A
        self.serie_a_tab = CompetitionTabWidget("Serie A")
        self.competition_stack.addWidget(self.serie_a_tab)

        # Serie B
        self.serie_b_tab = CompetitionTabWidget("Serie B")
        self.competition_stack.addWidget(self.serie_b_tab)

        # Serie C - 3 gironi
        for girone in ["A", "B", "C"]:
            tab_widget = CompetitionTabWidget(f"Serie C - Girone {girone}")
            self.serie_c_tabs[f"Girone {girone}"] = tab_widget
            self.competition_stack.addWidget(tab_widget)

        # Coppe Nazionali
        self.cups_tab = CupsTabWidget()
        self.competition_stack.addWidget(self.cups_tab)

    def on_competition_changed(self):
        """Gestisce cambio di competizione selezionata"""
        if not self.competition_selector:
            return

        current_data = self.competition_selector.currentData()

        if current_data == "serie_a" and self.serie_a_tab:
            self.competition_stack.setCurrentWidget(self.serie_a_tab)
        elif current_data == "serie_b" and self.serie_b_tab:
            self.competition_stack.setCurrentWidget(self.serie_b_tab)
        elif current_data == "serie_c_a" and "Girone A" in self.serie_c_tabs:
            self.competition_stack.setCurrentWidget(self.serie_c_tabs["Girone A"])
        elif current_data == "serie_c_b" and "Girone B" in self.serie_c_tabs:
            self.competition_stack.setCurrentWidget(self.serie_c_tabs["Girone B"])
        elif current_data == "serie_c_c" and "Girone C" in self.serie_c_tabs:
            self.competition_stack.setCurrentWidget(self.serie_c_tabs["Girone C"])
        elif current_data == "cups" and self.cups_tab:
            self.competition_stack.setCurrentWidget(self.cups_tab)

    def create_summary_panel(self, main_splitter: QSplitter):
        """Crea panel riepilogo"""
        summary_group = QGroupBox("📊 Riepilogo Competizioni")
        summary_layout = QVBoxLayout(summary_group)
        
        self.summary_text = QTextEdit()
        self.summary_text.setReadOnly(True)
        self.summary_text.setMaximumHeight(120)
        summary_layout.addWidget(self.summary_text)
        
        main_splitter.addWidget(summary_group)

    def load_season(self, season: Season, competition_manager: CompetitionManager,
                   calendar: MatchCalendar):
        """Carica stagione e competizioni"""
        self.season = season
        self.competition_manager = competition_manager
        self.calendar = calendar

        # Aggiorna interfacce
        if self.season_info:
            self.season_info.update_season_info(season)
        
        if self.calendar_widget:
            self.calendar_widget.load_calendar(calendar)
        
        self.update_competitions_display()
        self.update_summary_display()

    def update_competitions_display(self):
        """Aggiorna visualizzazione competizioni"""
        if not self.competition_manager:
            return

        # Aggiorna classifiche
        standings = self.competition_manager.get_current_standings()

        # Serie A
        if "Serie A" in standings and self.serie_a_tab:
            comp = self.competition_manager.get_competition("Serie A")
            if isinstance(comp, LeagueCompetition):
                self.serie_a_tab.standings_table.load_standings(
                    standings["Serie A"], 0, comp.relegation_spots
                )
                self.serie_a_tab.update_team_filter([s.team_name for s in standings["Serie A"]])
        
        # Serie B
        if "Serie B" in standings and self.serie_b_tab:
            comp = self.competition_manager.get_competition("Serie B")
            if isinstance(comp, LeagueCompetition):
                self.serie_b_tab.standings_table.load_standings(
                    standings["Serie B"], comp.promotion_spots, comp.relegation_spots
                )
                self.serie_b_tab.update_team_filter([s.team_name for s in standings["Serie B"]])

        # Serie C gironi
        for girone_name in ["Girone A", "Girone B", "Girone C"]:
            comp_name = f"Serie C - {girone_name}"
            if comp_name in standings and girone_name in self.serie_c_tabs:
                comp = self.competition_manager.get_competition(comp_name)
                if isinstance(comp, LeagueCompetition):
                    tab_widget = self.serie_c_tabs[girone_name]
                    tab_widget.standings_table.load_standings(
                        standings[comp_name], comp.promotion_spots, 0
                    )
                    tab_widget.update_team_filter([s.team_name for s in standings[comp_name]])

        # Aggiorna partite per ogni competizione
        self.update_matches_display()

        # Aggiorna coppe
        if self.cups_tab and self.competition_manager and self.calendar:
            self.cups_tab.update_cups_display(self.competition_manager, self.calendar)

    def update_matches_display(self):
        """Aggiorna visualizzazione partite per tutte le competizioni"""
        if not self.calendar:
            return

        # Aggiorna partite Serie A
        if self.serie_a_tab:
            serie_a_matches = self.calendar.get_matches_by_competition("Serie A")
            self.serie_a_tab.matches_table.load_matches(serie_a_matches[:20])  # Ultimi 20 match

        # Aggiorna partite Serie B
        if self.serie_b_tab:
            serie_b_matches = self.calendar.get_matches_by_competition("Serie B")
            self.serie_b_tab.matches_table.load_matches(serie_b_matches[:20])

        # Aggiorna partite gironi Serie C
        for girone_name in ["Girone A", "Girone B", "Girone C"]:
            if girone_name in self.serie_c_tabs:
                comp_name = f"Serie C - {girone_name}"
                girone_matches = self.calendar.get_matches_by_competition(comp_name)
                self.serie_c_tabs[girone_name].matches_table.load_matches(girone_matches[:20])

    def update_summary_display(self):
        """Aggiorna riepilogo competizioni"""
        if not self.competition_manager or not self.summary_text:
            return

        summary_text = "<h3>Riepilogo Competizioni</h3>"

        # Aggiungi informazioni su ciascuna competizione
        competitions = self.competition_manager.get_all_competitions()
        for comp in competitions:
            summary_text += f"<b>{comp.name}</b><br>"
            summary_text += f"  Squadre: {len(comp.teams)}<br>"

            # Calcola partite giocate in modo sicuro
            matches_played = 0
            total_matches = 0

            if hasattr(comp, 'matches'):
                matches_played = len([m for m in comp.matches if hasattr(m, 'played') and m.played])
                total_matches = len(comp.matches)
            elif hasattr(comp, 'get_competition_stats'):
                stats = comp.get_competition_stats()
                matches_played = stats.get('total_matches_played', 0)
                total_matches = stats.get('total_matches', 0)

            summary_text += f"  Partite: {matches_played}/{total_matches}<br><br>"

        self.summary_text.setHtml(summary_text)

    def refresh_displays(self):
        """Aggiorna tutte le visualizzazioni"""
        if self.season and self.season_info:
            self.season_info.update_season_info(self.season)

        self.update_competitions_display()
        self.update_summary_display()
