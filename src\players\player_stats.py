"""
Sistema di statistiche e sviluppo giocatori
"""
from typing import Dict, List, Optional
from enum import Enum
import datetime

from .player import Player, PlayerPosition
from ..core.utils import logger, clamp

class TrainingType(Enum):
    """Tipi di allenamento"""
    TECNICO = "Tecnico"
    FISICO = "Fisico"
    TATTICO = "Tattico"
    MENTALE = "Mentale"
    INDIVIDUALE = "Individuale"

class PlayerForm(Enum):
    """Forma attuale del giocatore"""
    PESSIMA = "Pessima"
    SCARSA = "Scarsa"
    NORMALE = "Normale"
    BUONA = "Buona"
    ECCELLENTE = "Eccellente"

class PlayerStatsManager:
    """Gestore delle statistiche e sviluppo giocatori"""

    def __init__(self):
        self.training_effects = self._initialize_training_effects()

    def _initialize_training_effects(self) -> Dict:
        """Inizializza effetti degli allenamenti sui vari attributi"""
        return {
            TrainingType.TECNICO: {
                "tecnica": 0.3,
                "passaggio": 0.3,
                "dribbling": 0.2,
                "tiro": 0.2
            },
            TrainingType.FISICO: {
                "velocita": 0.4,
                "forza": 0.4,
                "resistenza": 0.2
            },
            TrainingType.TATTICO: {
                "mentalita": 0.3,
                "passaggio": 0.2,
                "difesa": 0.3,
                "tecnica": 0.2
            },
            TrainingType.MENTALE: {
                "mentalita": 0.5,
                "tecnica": 0.2,
                "tiro": 0.3
            },
            TrainingType.INDIVIDUALE: {
                # Migliora attributi principali per posizione
                "general": 0.4
            }
        }

    def calculate_player_form(self, player: Player) -> PlayerForm:
        """Calcola forma attuale del giocatore"""
        # Basato su performance ultime partite, morale, fitness
        form_score = 0

        # Fattore morale (0-100)
        form_score += (player.morale - 50) / 10

        # Fattore fitness (0-100)
        form_score += (player.fitness - 75) / 5

        # Fattore voto medio stagionale
        if player.current_season_stats.presenze > 0:
            rating_bonus = (player.current_season_stats.voto_medio - 6.0) * 10
            form_score += rating_bonus

        # Fattore infortunio
        if player.is_injured:
            form_score -= 15

        # Converti in enum
        if form_score >= 20:
            return PlayerForm.ECCELLENTE
        elif form_score >= 10:
            return PlayerForm.BUONA
        elif form_score >= -5:
            return PlayerForm.NORMALE
        elif form_score >= -15:
            return PlayerForm.SCARSA
        else:
            return PlayerForm.PESSIMA

    def apply_training(self, player: Player, training_type: TrainingType,
                      training_quality: float = 1.0, days: int = 7):
        """Applica effetti dell'allenamento al giocatore"""

        if player.is_injured:
            logger.debug(f"{player.full_name} è infortunato, nessun miglioramento")
            return

        # Calcola probabilità di miglioramento basata su età
        improvement_base_chance = self._get_improvement_chance_by_age(player.age)

        # Modifica per qualità allenamento
        improvement_chance = improvement_base_chance * training_quality * (days / 7)

        # Ottieni attributi da migliorare
        attributes_to_improve = self.training_effects.get(training_type, {})

        improvements_made = []

        for attr_name, effect_strength in attributes_to_improve.items():
            if attr_name == "general":
                # Allenamento individuale migliora attributi principali per posizione
                self._apply_individual_training(player, improvement_chance * effect_strength)
            else:
                # Allenamento specifico
                final_chance = improvement_chance * effect_strength

                if self._should_improve_attribute(final_chance):
                    current_value = getattr(player.attributes, attr_name)

                    # Non può superare il potenziale (tranne casi rari)
                    max_possible = min(20, player.potential + 2)

                    if current_value < max_possible:
                        new_value = min(current_value + 1, max_possible)
                        setattr(player.attributes, attr_name, new_value)
                        improvements_made.append(attr_name)

        if improvements_made:
            logger.info(f"{player.full_name} migliorato in: {', '.join(improvements_made)}")

        # Migliora leggermente il morale con buoni allenamenti
        if training_quality > 1.0:
            player.morale = min(player.morale + 2, 100)

    def _get_improvement_chance_by_age(self, age: int) -> float:
        """Restituisce probabilità base di miglioramento per età"""
        if age <= 18:
            return 0.25  # Giovani migliorano molto
        elif age <= 21:
            return 0.15  # Buon miglioramento
        elif age <= 25:
            return 0.10  # Ancora possibile
        elif age <= 29:
            return 0.05  # Difficile ma possibile
        else:
            return 0.02  # Molto difficile

    def _should_improve_attribute(self, chance: float) -> bool:
        """Determina se un attributo dovrebbe migliorare"""
        import random
        return random.random() < chance

    def _apply_individual_training(self, player: Player, improvement_chance: float):
        """Applica allenamento individuale basato su posizione"""
        # Ottieni attributi principali per la posizione
        main_attributes = self._get_main_attributes_for_position(player.position)

        for attr_name in main_attributes:
            if self._should_improve_attribute(improvement_chance):
                current_value = getattr(player.attributes, attr_name)

                if current_value < min(20, player.potential):
                    new_value = current_value + 1
                    setattr(player.attributes, attr_name, new_value)
                    logger.debug(f"{player.full_name}: {attr_name} {current_value} -> {new_value}")

    def _get_main_attributes_for_position(self, position: PlayerPosition) -> List[str]:
        """Restituisce attributi principali per posizione"""
        position_attributes = {
            PlayerPosition.PORTIERE: ["portiere", "mentalita", "velocita"],
            PlayerPosition.DIFENSORE_CENTRALE: ["difesa", "forza", "mentalita"],
            PlayerPosition.DIFENSORE_SINISTRO: ["difesa", "velocita", "resistenza"],
            PlayerPosition.DIFENSORE_DESTRO: ["difesa", "velocita", "resistenza"],
            PlayerPosition.CENTROCAMPISTA_CENTRALE: ["passaggio", "tecnica", "mentalita"],
            PlayerPosition.CENTROCAMPISTA_SINISTRO: ["dribbling", "velocita", "passaggio"],
            PlayerPosition.CENTROCAMPISTA_DESTRO: ["dribbling", "velocita", "passaggio"],
            PlayerPosition.TREQUARTISTA: ["tecnica", "dribbling", "passaggio"],
            PlayerPosition.ATTACCANTE_SINISTRO: ["tiro", "velocita", "dribbling"],
            PlayerPosition.ATTACCANTE_DESTRO: ["tiro", "velocita", "dribbling"],
            PlayerPosition.CENTRAVANTI: ["tiro", "forza", "mentalita"]
        }

        return position_attributes.get(position, ["tecnica", "velocita", "mentalita"])

    def apply_age_decline(self, player: Player):
        """Applica declino dovuto all'età"""
        age = player.age

        # Declino inizia dopo i 30
        if age < 30:
            return

        decline_chance = 0.02  # 2% base

        if age >= 32:
            decline_chance = 0.05  # 5%
        if age >= 35:
            decline_chance = 0.10  # 10%
        if age >= 37:
            decline_chance = 0.20  # 20%

        # Attributi fisici declinano prima
        physical_attributes = ["velocita", "forza", "resistenza"]

        import random

        for attr_name in physical_attributes:
            if random.random() < decline_chance * 1.5:  # Fisico declina di più
                current_value = getattr(player.attributes, attr_name)
                if current_value > 1:
                    setattr(player.attributes, attr_name, current_value - 1)

        # Altri attributi
        other_attributes = ["tecnica", "passaggio", "dribbling", "tiro", "difesa"]

        for attr_name in other_attributes:
            if random.random() < decline_chance:
                current_value = getattr(player.attributes, attr_name)
                if current_value > 1:
                    setattr(player.attributes, attr_name, current_value - 1)

        # Mentalità e portiere possono rimanere stabili più a lungo
        mental_attributes = ["mentalita", "portiere"]

        for attr_name in mental_attributes:
            if random.random() < decline_chance * 0.5:  # Declino ridotto
                current_value = getattr(player.attributes, attr_name)
                if current_value > 1:
                    setattr(player.attributes, attr_name, current_value - 1)

    def update_player_morale(self, player: Player, factors: Dict[str, float]):
        """Aggiorna morale del giocatore basato su vari fattori"""
        morale_change = 0

        # Fattori possibili:
        # "playing_time": -10 to +10
        # "team_performance": -5 to +5
        # "contract_satisfaction": -10 to +10
        # "injury": -15 to 0
        # "training_quality": -3 to +3

        for factor, change in factors.items():
            morale_change += change

        # Applica cambiamento
        player.morale = clamp(player.morale + morale_change, 0, 100)

        logger.debug(f"{player.full_name} morale: {player.morale} (cambio: {morale_change:+.1f})")

    def calculate_player_value_change(self, player: Player,
                                    performance_factor: float = 1.0) -> int:
        """Calcola cambio valore di mercato del giocatore"""
        old_value = player.market_value

        # Fattori che influenzano il valore:
        # 1. Performance stagionale
        # 2. Età e sviluppo attributi
        # 3. Infortuni
        # 4. Morale

        value_multiplier = 1.0

        # Performance factor
        value_multiplier *= performance_factor

        # Fattore età/sviluppo
        if player.age <= 23:
            # Giovani aumentano di valore se migliorano
            current_overall = player.overall_rating
            if current_overall > player.potential * 0.8:
                value_multiplier *= 1.2
        elif player.age >= 32:
            # Veterani perdono valore
            value_multiplier *= 0.9

        # Fattore infortuni
        if player.is_injured:
            value_multiplier *= 0.8

        # Fattore morale
        if player.morale >= 80:
            value_multiplier *= 1.05
        elif player.morale <= 50:
            value_multiplier *= 0.95

        new_value = int(old_value * value_multiplier)
        value_change = new_value - old_value

        # Aggiorna valore
        player._market_value = new_value

        return value_change

    def generate_season_stats_report(self, players: List[Player]) -> Dict:
        """Genera report statistiche stagionali"""
        report = {
            "total_players": len(players),
            "top_scorers": [],
            "top_assists": [],
            "most_appearances": [],
            "highest_rated": [],
            "injuries": 0,
            "average_age": 0,
            "total_value": 0
        }

        if not players:
            return report

        # Calcola statistiche
        total_age = sum(p.age for p in players)
        report["average_age"] = total_age / len(players)

        report["total_value"] = sum(p.market_value for p in players)
        report["injuries"] = len([p for p in players if p.is_injured])

        # Top performers (solo se hanno giocato)
        active_players = [p for p in players if p.current_season_stats.presenze > 0]

        # Top marcatori
        top_scorers = sorted(active_players,
                           key=lambda p: p.current_season_stats.gol,
                           reverse=True)[:5]

        report["top_scorers"] = [(p.full_name, p.current_season_stats.gol)
                               for p in top_scorers if p.current_season_stats.gol > 0]

        # Top assist
        top_assists = sorted(active_players,
                           key=lambda p: p.current_season_stats.assist,
                           reverse=True)[:5]

        report["top_assists"] = [(p.full_name, p.current_season_stats.assist)
                               for p in top_assists if p.current_season_stats.assist > 0]

        # Più presenze
        most_appearances = sorted(active_players,
                                key=lambda p: p.current_season_stats.presenze,
                                reverse=True)[:5]

        report["most_appearances"] = [(p.full_name, p.current_season_stats.presenze)
                                    for p in most_appearances]

        # Voto più alto
        highest_rated = sorted(active_players,
                             key=lambda p: p.current_season_stats.voto_medio,
                             reverse=True)[:5]

        report["highest_rated"] = [(p.full_name, p.current_season_stats.voto_medio)
                                 for p in highest_rated]

        return report

# Istanza globale del gestore statistiche
player_stats_manager = PlayerStatsManager()