"""
Widget tabella classifiche
"""
from PyQt6.QtWidgets import QTableWidget, QTableWidgetItem, QHeaderView
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont
from typing import List
from src.competitions.competition import LeagueStanding
from src.core.config import COLOR_SUCCESS, COLOR_WARNING, COLOR_ERROR


class StandingsTableWidget(QTableWidget):
    """Tabella classifiche"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_table()

    def setup_table(self):
        """Configura tabella classifiche"""
        headers = ["Pos", "Squadra", "G", "V", "N", "S", "GF", "GS", "DR", "Pt"]
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)

        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.setSortingEnabled(False)  # Ordinamento manuale

        # Ridimensiona colonne
        header = self.horizontalHeader()
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # Nome squadra
        
        # Colonne numeriche più strette
        for col in [0, 2, 3, 4, 5, 6, 7, 8, 9]:
            header.setSectionResizeMode(col, QHeaderView.ResizeMode.ResizeToContents)

    def load_standings(self, standings: List[LeagueStanding], 
                      promotion_spots: int = 0, relegation_spots: int = 0):
        """Carica classifica"""
        self.setRowCount(len(standings))
        
        for row, standing in enumerate(standings):
            # Posizione
            pos_item = QTableWidgetItem(str(standing.position))
            pos_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            
            # Colora zona promozione/retrocessione
            if promotion_spots > 0 and standing.position <= promotion_spots:
                pos_item.setBackground(Qt.GlobalColor.green)
                pos_item.setForeground(Qt.GlobalColor.white)
            elif relegation_spots > 0 and standing.position > len(standings) - relegation_spots:
                pos_item.setBackground(Qt.GlobalColor.red)
                pos_item.setForeground(Qt.GlobalColor.white)
            
            self.setItem(row, 0, pos_item)
            
            # Nome squadra
            team_item = QTableWidgetItem(standing.team_name)
            if promotion_spots > 0 and standing.position <= promotion_spots:
                team_item.setFont(QFont("Arial", 9, QFont.Weight.Bold))
            elif relegation_spots > 0 and standing.position > len(standings) - relegation_spots:
                team_item.setFont(QFont("Arial", 9, QFont.Weight.Bold))
            self.setItem(row, 1, team_item)
            
            # Statistiche numeriche
            stats = [
                standing.matches_played,
                standing.wins,
                standing.draws,
                standing.losses,
                standing.goals_for,
                standing.goals_against,
                standing.goal_difference,
                standing.points
            ]
            
            for col, stat in enumerate(stats, 2):
                item = QTableWidgetItem(str(stat))
                item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                
                # Evidenzia punti
                if col == 9:  # Colonna punti
                    item.setFont(QFont("Arial", 9, QFont.Weight.Bold))
                
                self.setItem(row, col, item)

    def get_selected_team(self) -> str:
        """Restituisce squadra selezionata"""
        current_row = self.currentRow()
        if current_row >= 0:
            team_item = self.item(current_row, 1)
            return team_item.text() if team_item else ""
        return ""
