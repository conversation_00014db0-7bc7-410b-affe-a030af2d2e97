"""
Test del sistema giocatori
"""
import sys
from pathlib import Path

# Aggiunge src al path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from src.core.config import ensure_directories
from src.data.data_loader import data_loader
from src.players.player_generator import player_generator
from src.players.player import PlayerPosition
from src.players.player_stats import player_stats_manager, TrainingType

def test_single_player_generation():
    """Test generazione singolo giocatore"""
    print("=== TEST GENERAZIONE SINGOLO GIOCATORE ===")

    # Genera giocatore casuale
    player = player_generator.generate_player()
    print(f"Generato: {player}")
    print(f"Attributi Overall: {player.overall_rating}")
    print(f"Potenziale: {player.potential}")
    print(f"Valore mercato: {player.market_value:,} euro")
    print(f"Nazione: {player.nationality}")

    # Genera portiere specifico
    goalkeeper = player_generator.generate_player(position=PlayerPosition.PORTIERE)
    print(f"\nPortiere: {goalkeeper}")
    print(f"Attributo portiere: {goalkeeper.attributes.portiere}")

    # Genera centravanti italiano
    striker = player_generator.generate_player(
        position=PlayerPosition.CENTRAVANTI,
        nationality="Italia"
    )
    print(f"\nCentravanti italiano: {striker}")

def test_team_generation():
    """Test generazione rosa squadra"""
    print("\n=== TEST GENERAZIONE ROSA SQUADRA ===")

    # Genera squadra Serie B
    squad = player_generator.generate_team_squad(team_level=2, squad_size=25)

    print(f"Rosa generata: {len(squad)} giocatori")

    # Analizza composizione
    positions_count = {}
    nationalities_count = {}
    ages_sum = 0

    for player in squad:
        # Posizioni
        pos = player.position.value
        positions_count[pos] = positions_count.get(pos, 0) + 1

        # Nazionalità
        nat = player.nationality
        nationalities_count[nat] = nationalities_count.get(nat, 0) + 1

        # Età
        ages_sum += player.age

    print("\nComposizione per posizione:")
    for pos, count in sorted(positions_count.items()):
        print(f"  {pos}: {count}")

    print(f"\nEtà media: {ages_sum/len(squad):.1f} anni")

    print("\nPrime 5 nazionalità:")
    sorted_nations = sorted(nationalities_count.items(), key=lambda x: x[1], reverse=True)
    for nation, count in sorted_nations[:5]:
        print(f"  {nation}: {count}")

    # Mostra giocatori più forti
    sorted_by_overall = sorted(squad, key=lambda p: p.overall_rating, reverse=True)
    print(f"\nTop 5 giocatori per overall:")
    for player in sorted_by_overall[:5]:
        print(f"  {player.overall_rating}: {player}")

def test_player_stats_system():
    """Test sistema statistiche giocatori"""
    print("\n=== TEST SISTEMA STATISTICHE ===")

    # Genera alcuni giocatori
    players = []
    for i in range(5):
        player = player_generator.generate_player()
        players.append(player)

        # Simula alcune statistiche
        player.current_season_stats.presenze = i * 3 + 5
        player.current_season_stats.gol = i * 2
        player.current_season_stats.assist = i
        player.current_season_stats.voto_medio = 6.0 + (i * 0.3)

    # Genera report
    report = player_stats_manager.generate_season_stats_report(players)

    print(f"Report stagionale:")
    print(f"  Giocatori totali: {report['total_players']}")
    print(f"  Età media: {report['average_age']:.1f}")
    print(f"  Valore totale: {report['total_value']:,} euro")

    if report['top_scorers']:
        print("  Top marcatori:")
        for name, goals in report['top_scorers']:
            print(f"    {name}: {goals} gol")

def test_training_system():
    """Test sistema allenamento"""
    print("\n=== TEST SISTEMA ALLENAMENTO ===")

    # Crea giovane promessa
    young_player = player_generator.generate_player(age_range=(18, 20))

    print(f"Prima dell'allenamento: {young_player}")
    print(f"Attributi tecnici: T={young_player.attributes.tecnica}, "
          f"V={young_player.attributes.velocita}, F={young_player.attributes.forza}")

    # Applica allenamento tecnico per 4 settimane
    print(f"\nApplicando allenamento tecnico per 4 settimane...")
    for week in range(4):
        player_stats_manager.apply_training(
            young_player,
            TrainingType.TECNICO,
            training_quality=1.2,
            days=7
        )

    print(f"Dopo allenamento: {young_player}")
    print(f"Attributi tecnici: T={young_player.attributes.tecnica}, "
          f"V={young_player.attributes.velocita}, F={young_player.attributes.forza}")

    # Test forma
    form = player_stats_manager.calculate_player_form(young_player)
    print(f"Forma attuale: {form.value}")

def test_player_development():
    """Test sviluppo giocatore nel tempo"""
    print("\n=== TEST SVILUPPO GIOCATORE ===")

    # Crea giocatore giovane
    player = player_generator.generate_player(age_range=(17, 19))

    print(f"Giocatore iniziale: {player}")
    print(f"Overall: {player.overall_rating}, Potenziale: {player.potential}")

    # Simula 3 anni di sviluppo
    for year in range(3):
        print(f"\n--- Anno {year + 1} ---")

        # Allenamenti regolari
        for month in range(10):  # 10 mesi di stagione
            if player.age <= 25:  # Solo se ancora giovane
                player_stats_manager.apply_training(
                    player, TrainingType.INDIVIDUALE, training_quality=1.1, days=7
                )

        # Aggiorna età (simulato)
        player.birth_date = player.birth_date.replace(year=player.birth_date.year - 1)

        print(f"Età {player.age}: Overall {player.overall_rating}")
        print(f"Valore mercato: {player.market_value:,} euro")

if __name__ == "__main__":
    try:
        ensure_directories()

        # Carica dati necessari
        if not data_loader.load_all_data():
            print("Errore: impossibile caricare i dati")
            sys.exit(1)

        # Esegui test
        test_single_player_generation()
        test_team_generation()
        test_player_stats_system()
        test_training_system()
        test_player_development()

        print("\n=== TUTTI I TEST GIOCATORI COMPLETATI ===")

    except Exception as e:
        print(f"Errore durante i test: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)