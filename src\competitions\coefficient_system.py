"""
Sistema UEFA Coefficient per ranking nazioni e assegnazione posti europei
"""
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json

from ..core.utils import logger

class UEFAAssociation(Enum):
    """Livelli associazioni UEFA per qualificazioni"""
    TOP_4 = "Top 4"           # Inghilterra, Spagna, Germania, Italia
    TOP_6 = "Top 6"           # + Francia, Portogallo
    TOP_15 = "Top 15"         # Olanda, Belgio, Austria, ecc.
    OTHERS = "Others"         # Resto delle associazioni

@dataclass
class CountryCoefficient:
    """Dati coefficiente di una nazione"""
    country: str
    coefficient: float
    ranking: int
    association_level: UEFAAssociation
    champions_league_spots: int
    europa_league_spots: int
    conference_league_spots: int

class UEFACoefficientSystem:
    """Sistema gestione coefficienti UEFA e qualificazioni"""

    def __init__(self):
        self.coefficients: Dict[str, CountryCoefficient] = {}
        self.setup_historical_coefficients()

    def setup_historical_coefficients(self):
        """Setup coefficienti storici UEFA basati su dati reali 2020-2025"""

        # Coefficienti basati su ranking UEFA reale
        historical_data = [
            # Rank, Country, Coefficient, CL spots, EL spots, ECL spots
            (1, "Inghilterra", 95.125, 4, 3, 1),
            (2, "Spagna", 88.714, 4, 2, 1),
            (3, "Germania", 79.214, 4, 2, 1),
            (4, "Italia", 75.999, 4, 2, 1),
            (5, "<PERSON>ancia", 58.998, 3, 2, 2),
            (6, "Porto<PERSON><PERSON>", 50.316, 2, 2, 2),
            (7, "Paesi Bassi", 47.200, 2, 2, 2),
            (8, "Belgio", 38.600, 1, 2, 2),
            (9, "Austria", 34.125, 1, 1, 2),
            (10, "Turchia", 33.100, 1, 1, 2),
            (11, "Repubblica Ceca", 32.375, 1, 1, 2),
            (12, "Scozia", 31.125, 1, 2, 1),
            (13, "Svizzera", 28.275, 1, 1, 2),
            (14, "Norvegia", 26.375, 1, 1, 2),
            (15, "Danimarca", 25.400, 1, 1, 2),
            (16, "Serbia", 24.250, 1, 1, 1),
            (17, "Ucraina", 22.633, 1, 1, 1),
            (18, "Croazia", 21.375, 1, 1, 1),
            (19, "Grecia", 20.200, 1, 1, 1),
            (20, "Israele", 18.750, 1, 1, 1),
            # Continuazione per altre nazioni...
            (21, "Cipro", 17.875, 0, 1, 1),
            (22, "Svezia", 16.875, 0, 1, 1),
            (23, "Polonia", 16.500, 0, 1, 1),
            (24, "Ungheria", 15.750, 0, 1, 1),
            (25, "Romania", 14.583, 0, 1, 1),
            (26, "Bulgaria", 13.875, 0, 1, 1),
            (27, "Slovacchia", 13.375, 0, 1, 1),
            (28, "Azerbaigian", 12.750, 0, 1, 1),
            (29, "Kazakhstan", 11.750, 0, 1, 1),
            (30, "Slovenia", 11.625, 0, 1, 1)
        ]

        for rank, country, coeff, cl, el, ecl in historical_data:
            # Determina livello associazione
            if rank <= 4:
                level = UEFAAssociation.TOP_4
            elif rank <= 6:
                level = UEFAAssociation.TOP_6
            elif rank <= 15:
                level = UEFAAssociation.TOP_15
            else:
                level = UEFAAssociation.OTHERS

            self.coefficients[country] = CountryCoefficient(
                country=country,
                coefficient=coeff,
                ranking=rank,
                association_level=level,
                champions_league_spots=cl,
                europa_league_spots=el,
                conference_league_spots=ecl
            )

        logger.info(f"Setup {len(self.coefficients)} coefficienti UEFA")

    def get_country_coefficient(self, country: str) -> Optional[CountryCoefficient]:
        """Restituisce coefficiente di una nazione"""
        return self.coefficients.get(country)

    def get_champions_league_qualified_countries(self) -> List[str]:
        """Restituisce paesi con posti Champions League"""
        qualified = []
        for country, coeff in self.coefficients.items():
            if coeff.champions_league_spots > 0:
                qualified.append(country)
        return qualified

    def get_europa_league_qualified_countries(self) -> List[str]:
        """Restituisce paesi con posti Europa League"""
        qualified = []
        for country, coeff in self.coefficients.items():
            if coeff.europa_league_spots > 0:
                qualified.append(country)
        return qualified

    def get_conference_league_qualified_countries(self) -> List[str]:
        """Restituisce paesi con posti Conference League"""
        qualified = []
        for country, coeff in self.coefficients.items():
            if coeff.conference_league_spots > 0:
                qualified.append(country)
        return qualified

    def get_qualified_teams_for_competition(self, country: str, competition: str) -> List[int]:
        """Restituisce posizioni qualificate per una competizione"""
        coeff = self.get_country_coefficient(country)
        if not coeff:
            return []

        positions = []

        if competition.lower() == "champions league":
            # Posizioni 1-N per Champions League
            for i in range(1, coeff.champions_league_spots + 1):
                positions.append(i)

        elif competition.lower() == "europa league":
            # Posizioni dopo CL spots
            start_pos = coeff.champions_league_spots + 1
            for i in range(start_pos, start_pos + coeff.europa_league_spots):
                positions.append(i)

        elif competition.lower() == "conference league":
            # Posizioni dopo CL + EL spots
            start_pos = coeff.champions_league_spots + coeff.europa_league_spots + 1
            for i in range(start_pos, start_pos + coeff.conference_league_spots):
                positions.append(i)

        return positions

    def get_all_uefa_qualified_teams(self) -> Dict[str, Dict[str, List[int]]]:
        """Restituisce tutte le squadre qualificate UEFA per paese e competizione"""
        all_qualified = {}

        for country in self.coefficients.keys():
            country_qualified = {
                "Champions League": self.get_qualified_teams_for_competition(country, "Champions League"),
                "Europa League": self.get_qualified_teams_for_competition(country, "Europa League"),
                "Conference League": self.get_qualified_teams_for_competition(country, "Conference League")
            }

            # Solo se ha qualificazioni
            if any(positions for positions in country_qualified.values()):
                all_qualified[country] = country_qualified

        return all_qualified

    def get_total_uefa_teams_count(self) -> Dict[str, int]:
        """Restituisce conteggio totale squadre per competizione"""
        totals = {
            "Champions League": 0,
            "Europa League": 0,
            "Conference League": 0
        }

        for coeff in self.coefficients.values():
            totals["Champions League"] += coeff.champions_league_spots
            totals["Europa League"] += coeff.europa_league_spots
            totals["Conference League"] += coeff.conference_league_spots

        return totals

    def get_top_associations(self, limit: int = 15) -> List[CountryCoefficient]:
        """Restituisce top associazioni per coefficiente"""
        sorted_coeffs = sorted(
            self.coefficients.values(),
            key=lambda x: x.ranking
        )
        return sorted_coeffs[:limit]

    def update_coefficient(self, country: str, new_coefficient: float):
        """Aggiorna coefficiente di una nazione (per simulazione futura)"""
        if country in self.coefficients:
            self.coefficients[country].coefficient = new_coefficient
            # Ricalcola ranking (implementazione futura)
            logger.info(f"Aggiornato coefficiente {country}: {new_coefficient}")

    def export_coefficients_data(self) -> Dict:
        """Esporta dati coefficienti per visualizzazione"""
        export_data = {
            "coefficients": {},
            "totals": self.get_total_uefa_teams_count(),
            "qualified_teams": self.get_all_uefa_qualified_teams()
        }

        for country, coeff in self.coefficients.items():
            export_data["coefficients"][country] = {
                "coefficient": coeff.coefficient,
                "ranking": coeff.ranking,
                "level": coeff.association_level.value,
                "spots": {
                    "champions_league": coeff.champions_league_spots,
                    "europa_league": coeff.europa_league_spots,
                    "conference_league": coeff.conference_league_spots
                }
            }

        return export_data

# Istanza globale del sistema coefficienti
uefa_coefficient_system = UEFACoefficientSystem()