"""
Test per verificare la correzione del bug critico del GameEngine.
Bug: Le partite programmate nel giorno corrente non vengono mai simulate.
"""

import datetime
import sys
import os

# Aggiungi il percorso src al path per importare i moduli
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.core.game_engine import GameEngine, StopPoint, StopPointType
from src.competitions.season import Season
from src.competitions.calendar import MatchCalendar, Match, generate_full_season_calendar
from src.competitions.competition import CompetitionManager, setup_italian_competitions
from src.data.data_loader import DataLoader


def test_current_day_match_simulation():
    """Test che verifica che le partite del giorno corrente vengano simulate"""
    
    print("=== TEST: Simulazione partite del giorno corrente ===")
    
    # Setup base del gioco
    data_loader = DataLoader()
    data_loader.load_all_data()

    # Setup squadre per livello
    teams_by_level = {
        1: [team["nome"] for team in data_loader.get_serie_a_teams()],
        2: [team["nome"] for team in data_loader.get_serie_b_teams()]
    }
    
    # Crea stagione con data specifica
    test_date = datetime.date(2024, 8, 25)  # Domenica, giorno tipico per le partite
    season = Season(2024)
    season.current_date = test_date  # Imposta data corrente per il test
    
    # Setup competizioni
    serie_c_groups = data_loader.get_serie_c_groups()
    competition_manager = setup_italian_competitions(season, teams_by_level, serie_c_groups)
    
    # Genera calendario
    calendar = generate_full_season_calendar(season, competition_manager)
    
    # Verifica che ci siano partite per la data di test
    matches_today = calendar.get_matches_for_date(test_date)
    print(f"Partite trovate per {test_date}: {len(matches_today)}")
    
    if not matches_today:
        print("ATTENZIONE: Nessuna partita trovata per la data di test")
        # Aggiungiamo manualmente una partita per il test
        test_match = Match(
            home_team="Juventus",
            away_team="Inter Milan",
            date=test_date,
            time="20:45",
            competition="Serie A",
            matchday=1
        )
        calendar.matches.append(test_match)
        matches_today = [test_match]
        print(f"Aggiunta partita di test: {test_match.home_team} vs {test_match.away_team}")
    
    # Crea GameEngine con Juventus come squadra del giocatore
    game_engine = GameEngine(
        season=season,
        competition_manager=competition_manager,
        calendar=calendar,
        player_team="Juventus"
    )
    
    print(f"Data corrente stagione: {season.current_date}")
    print(f"GameEngine inizializzato con {len(game_engine.stop_points)} stop points")
    
    # Trova stop point per oggi
    today_stop_points = [sp for sp in game_engine.stop_points if sp.date == test_date]
    print(f"Stop points per oggi: {len(today_stop_points)}")
    
    if today_stop_points:
        stop_point = today_stop_points[0]
        print(f"Stop point trovato: {stop_point.description} - {stop_point.date}")
    
    # Verifica stato iniziale delle partite
    print("\n=== STATO INIZIALE PARTITE ===")
    for i, match in enumerate(matches_today):
        print(f"Partita {i+1}: {match.home_team} vs {match.away_team} - Giocata: {match.played}")
    
    # Esegui simulazione fino al prossimo stop point
    print("\n=== ESECUZIONE SIMULAZIONE ===")
    
    # Cattura i segnali emessi
    matches_simulated = []
    
    def on_match_simulated(result):
        matches_simulated.append(result)
        print(f"SEGNALE: Partita simulata - {result.get('home_team', 'N/A')} {result.get('home_score', 'N/A')}-{result.get('away_score', 'N/A')} {result.get('away_team', 'N/A')}")
    
    def on_simulation_stopped(reason, data):
        print(f"SEGNALE: Simulazione fermata - Motivo: {reason}")
        if data:
            print(f"Dati: {data}")
    
    # Collega i segnali
    game_engine.match_simulated.connect(on_match_simulated)
    game_engine.simulation_stopped.connect(on_simulation_stopped)
    
    # Esegui la simulazione
    game_engine.run_simulation_until_next_stop()
    
    # Verifica risultati
    print("\n=== VERIFICA RISULTATI ===")
    
    # Controlla se le partite sono state simulate
    matches_after = calendar.get_matches_for_date(test_date)
    played_matches = [m for m in matches_after if m.played]
    
    print(f"Partite giocate dopo simulazione: {len(played_matches)}")
    
    for match in played_matches:
        print(f"✓ {match.home_team} {match.home_score}-{match.away_score} {match.away_team}")
    
    # Verifica che i segnali siano stati emessi
    print(f"Segnali match_simulated ricevuti: {len(matches_simulated)}")
    
    # Test risultato
    if len(played_matches) > 0:
        print("\n✅ TEST SUPERATO: Le partite del giorno corrente sono state simulate!")
        return True
    else:
        print("\n❌ TEST FALLITO: Le partite del giorno corrente NON sono state simulate!")
        return False


def test_next_day_match_simulation():
    """Test di controllo: verifica che le partite del giorno successivo funzionino normalmente"""
    
    print("\n=== TEST CONTROLLO: Simulazione partite del giorno successivo ===")
    
    # Setup base del gioco
    data_loader = DataLoader()
    data_loader.load_all_data()

    # Setup squadre per livello
    teams_by_level = {
        1: [team["nome"] for team in data_loader.get_serie_a_teams()],
        2: [team["nome"] for team in data_loader.get_serie_b_teams()]
    }
    
    # Crea stagione con data un giorno prima
    test_date = datetime.date(2024, 8, 24)  # Sabato
    match_date = datetime.date(2024, 8, 25)  # Domenica - partita domani
    season = Season(2024)
    season.current_date = test_date  # Imposta data corrente per il test
    
    # Setup competizioni
    serie_c_groups = data_loader.get_serie_c_groups()
    competition_manager = setup_italian_competitions(season, teams_by_level, serie_c_groups)
    
    # Genera calendario
    calendar = generate_full_season_calendar(season, competition_manager)
    
    # Aggiungi partita per domani
    test_match = Match(
        home_team="Juventus",
        away_team="AC Milan",
        date=match_date,
        time="20:45",
        competition="Serie A",
        matchday=1
    )
    calendar.matches.append(test_match)
    
    # Crea GameEngine
    game_engine = GameEngine(
        season=season,
        competition_manager=competition_manager,
        calendar=calendar,
        player_team="Juventus"
    )
    
    print(f"Data corrente: {season.current_date}")
    print(f"Data partita: {match_date}")
    
    # Esegui simulazione
    matches_simulated = []
    
    def on_match_simulated(result):
        matches_simulated.append(result)
        print(f"SEGNALE: Partita simulata - {result.get('home_team', 'N/A')} {result.get('home_score', 'N/A')}-{result.get('away_score', 'N/A')} {result.get('away_team', 'N/A')}")
    
    game_engine.match_simulated.connect(on_match_simulated)
    game_engine.run_simulation_until_next_stop()
    
    # Verifica risultati
    if test_match.played:
        print("✅ TEST CONTROLLO SUPERATO: Le partite del giorno successivo funzionano correttamente!")
        return True
    else:
        print("❌ TEST CONTROLLO FALLITO: Problema anche con le partite del giorno successivo!")
        return False


if __name__ == "__main__":
    print("Avvio test per la correzione del bug del GameEngine...")
    
    # Esegui i test
    test1_passed = test_current_day_match_simulation()
    test2_passed = test_next_day_match_simulation()
    
    print("\n" + "="*60)
    print("RIEPILOGO RISULTATI TEST:")
    print(f"Test partite giorno corrente: {'✅ SUPERATO' if test1_passed else '❌ FALLITO'}")
    print(f"Test partite giorno successivo: {'✅ SUPERATO' if test2_passed else '❌ FALLITO'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 TUTTI I TEST SUPERATI! Il bug è stato corretto con successo!")
    else:
        print("\n⚠️  ALCUNI TEST FALLITI. Verificare la correzione.")
