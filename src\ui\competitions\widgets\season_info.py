"""
Widget informazioni stagione
"""
from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel, QProgressBar, QGroupBox
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont
from typing import Optional
from src.competitions.season import Season
from src.core.config import COLOR_PRIMARY, HEADER_FONT_SIZE
from src.core.utils import format_date_italian


class SeasonInfoWidget(QWidget):
    """Widget informazioni stagione"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.season: Optional[Season] = None
        self.setup_ui()

    def setup_ui(self):
        """Configura interfaccia"""
        layout = QVBoxLayout(self)
        
        # Gruppo informazioni stagione
        season_group = QGroupBox("📅 Informazioni Stagione")
        season_layout = QVBoxLayout(season_group)
        
        # Nome stagione
        self.season_name = QLabel("Stagione non caricata")
        self.season_name.setFont(QFont("Arial", HEADER_FONT_SIZE, QFont.Weight.Bold))
        self.season_name.setStyleSheet(f"color: {COLOR_PRIMARY};")
        self.season_name.setAlignment(Qt.AlignmentFlag.AlignCenter)
        season_layout.addWidget(self.season_name)
        
        # Data corrente
        self.current_date = QLabel("Data: --/--/----")
        self.current_date.setAlignment(Qt.AlignmentFlag.AlignCenter)
        season_layout.addWidget(self.current_date)
        
        # Progresso stagione
        self.season_progress = QProgressBar()
        self.season_progress.setTextVisible(True)
        self.season_progress.setFormat("Progresso: %p%")
        season_layout.addWidget(self.season_progress)
        
        # Informazioni date chiave
        self.key_dates = QLabel("Date chiave non disponibili")
        self.key_dates.setWordWrap(True)
        self.key_dates.setAlignment(Qt.AlignmentFlag.AlignLeft)
        season_layout.addWidget(self.key_dates)
        
        layout.addWidget(season_group)

    def update_season_info(self, season: Season):
        """Aggiorna informazioni stagione"""
        self.season = season
        
        # Nome stagione
        self.season_name.setText(f"Stagione {season.season_name}")
        
        # Data corrente
        current_date_str = format_date_italian(season.current_date)
        self.current_date.setText(f"Data: {current_date_str}")
        
        # Calcola progresso stagione
        total_days = (season.dates.season_end - season.dates.season_start).days
        current_days = (season.current_date - season.dates.season_start).days
        progress = min(100, max(0, int((current_days / total_days) * 100)))
        
        self.season_progress.setValue(progress)
        
        # Date chiave
        key_dates_text = self._format_key_dates(season)
        self.key_dates.setText(key_dates_text)

    def _format_key_dates(self, season: Season) -> str:
        """Formatta date chiave della stagione"""
        dates = season.dates
        
        key_dates = [
            f"<b>Inizio Stagione:</b> {format_date_italian(dates.season_start)}",
            f"<b>Inizio Campionati:</b> {format_date_italian(dates.league_start)}",
            f"<b>Mercato Invernale:</b> {format_date_italian(dates.winter_window_start)} - {format_date_italian(dates.winter_window_end)}",
            f"<b>Fine Campionati:</b> {format_date_italian(dates.league_end)}",
            f"<b>Fine Stagione:</b> {format_date_italian(dates.season_end)}"
        ]
        
        return "<br>".join(key_dates)

    def get_season_status(self) -> str:
        """Restituisce stato attuale della stagione"""
        if not self.season:
            return "Stagione non caricata"
        
        current = self.season.current_date
        dates = self.season.dates
        
        if current < dates.league_start:
            return "Pre-stagione"
        elif current < dates.winter_window_start:
            return "Prima parte stagione"
        elif current < dates.winter_window_end:
            return "Mercato invernale"
        elif current < dates.league_end:
            return "Seconda parte stagione"
        elif current < dates.season_end:
            return "Fine campionati"
        else:
            return "Stagione conclusa"
