"""
Widget Calendario Avanzato stile EA Sports/Football Manager
Permette navigazione temporale intelligente con visualizzazione partite
"""
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QCalendarWidget,
                            QLabel, QPushButton, QTextEdit, QProgressBar, QDialog,
                            QSplitter, QGroupBox, QListWidget, QListWidgetItem,
                            QMessageBox, QFrame)
from PyQt6.QtCore import Qt, QDate, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QTextCharFormat, QColor, QPalette

import datetime
from typing import Optional, Dict, List

from ..core.config import COLOR_PRIMARY, COLOR_SUCCESS, COLOR_WARNING, COLOR_ERROR
from ..core.utils import logger
from ..competitions.season import Season
from ..competitions.competition import CompetitionManager
from ..competitions.calendar import MatchCalendar, Match

class CustomCalendarWidget(QCalendarWidget):
    """QCalendarWidget personalizzato con evidenziazione partite"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.match_calendar = None
        self.setup_calendar()

    def setup_calendar(self):
        """Configura il calendario"""
        self.setGridVisible(True)
        self.setVerticalHeaderFormat(QCalendarWidget.VerticalHeaderFormat.NoVerticalHeader)

        # Formato per gli header
        header_format = QTextCharFormat()
        header_format.setFontWeight(QFont.Weight.Bold)
        self.setHeaderTextFormat(header_format)

    def set_match_calendar(self, match_calendar: MatchCalendar):
        """Imposta il calendario partite per evidenziazione"""
        self.match_calendar = match_calendar
        self.refresh_match_highlights()

    def refresh_match_highlights(self):
        """Aggiorna evidenziazione partite per il mese corrente"""
        if not self.match_calendar:
            return

        # Ottieni anno e mese correnti del widget
        current_page = self.monthShown(), self.yearShown()

        # Rimuovi formattazione precedente
        self.setDateTextFormat(QDate(), QTextCharFormat())

        # Ottieni partite del mese
        matches_by_day = self.match_calendar.get_matches_by_month(current_page[1], current_page[0])

        for day, matches in matches_by_day.items():
            date = QDate(current_page[1], current_page[0], day)
            date_info = self.match_calendar.get_match_density_for_date(datetime.date(current_page[1], current_page[0], day))

            # Crea formato in base al tipo di competizione
            format = QTextCharFormat()
            format.setFontWeight(QFont.Weight.Bold)

            # Sistema colori per competizioni
            if date_info["primary_type"] == "uefa":
                format.setBackground(QColor(255, 215, 0, 100))  # Oro trasparente
                format.setForeground(QColor(184, 134, 11))       # Oro scuro
            elif date_info["primary_type"] == "cup":
                format.setBackground(QColor(135, 206, 250, 100)) # Azzurro trasparente
                format.setForeground(QColor(25, 25, 112))        # Blu scuro
            elif date_info["primary_type"] == "league":
                format.setBackground(QColor(144, 238, 144, 100)) # Verde trasparente
                format.setForeground(QColor(0, 100, 0))          # Verde scuro
            else:
                format.setBackground(QColor(220, 220, 220, 100)) # Grigio trasparente
                format.setForeground(QColor(105, 105, 105))      # Grigio scuro

            # Intensità basata su numero partite
            if date_info["count"] > 2:
                format.setFontWeight(QFont.Weight.ExtraBold)
                format.setUnderlineStyle(QTextCharFormat.UnderlineStyle.SingleUnderline)

            self.setDateTextFormat(date, format)

    def paintCell(self, painter, rect, date):
        """Override per aggiungere indicatori personalizzati"""
        super().paintCell(painter, rect, date)

        if not self.match_calendar:
            return

        # Converti QDate in datetime.date
        py_date = datetime.date(date.year(), date.month(), date.day())
        date_info = self.match_calendar.get_match_density_for_date(py_date)

        # Disegna indicatore numero partite nell'angolo
        if date_info["count"] > 0:
            painter.save()

            # Font piccolo per il numero
            font = painter.font()
            font.setPointSize(8)
            font.setBold(True)
            painter.setFont(font)

            # Colore basato su competizione
            if date_info["primary_type"] == "uefa":
                painter.setPen(QColor(184, 134, 11))
            elif date_info["primary_type"] == "cup":
                painter.setPen(QColor(25, 25, 112))
            elif date_info["primary_type"] == "league":
                painter.setPen(QColor(0, 100, 0))
            else:
                painter.setPen(QColor(105, 105, 105))

            # Disegna numero nell'angolo in alto a destra
            text_rect = rect.adjusted(rect.width() - 12, 2, 0, 0)
            painter.drawText(text_rect, Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignTop, str(date_info["count"]))

            painter.restore()

class MatchPreviewWidget(QWidget):
    """Pannello anteprima partite per data selezionata"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.selected_date = None
        self.match_calendar = None
        self.setup_ui()

    def setup_ui(self):
        """Setup interfaccia"""
        layout = QVBoxLayout(self)

        # Titolo data selezionata
        self.date_label = QLabel("Seleziona una data")
        date_font = QFont()
        date_font.setPointSize(12)
        date_font.setBold(True)
        self.date_label.setFont(date_font)
        self.date_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.date_label)

        # Lista partite
        matches_group = QGroupBox("Partite Programmate")
        matches_layout = QVBoxLayout(matches_group)

        self.matches_list = QListWidget()
        self.matches_list.setMaximumHeight(200)
        matches_layout.addWidget(self.matches_list)

        layout.addWidget(matches_group)

        # Info competizioni
        competitions_group = QGroupBox("Competizioni")
        competitions_layout = QVBoxLayout(competitions_group)

        self.competitions_info = QLabel("Nessuna partita")
        self.competitions_info.setWordWrap(True)
        competitions_layout.addWidget(self.competitions_info)

        layout.addWidget(competitions_group)

        layout.addStretch()

    def set_match_calendar(self, match_calendar: MatchCalendar):
        """Imposta calendario partite"""
        self.match_calendar = match_calendar

    def update_preview(self, date: datetime.date):
        """Aggiorna anteprima per data specificata"""
        self.selected_date = date

        # Aggiorna titolo
        date_str = date.strftime("%d/%m/%Y")
        weekday = ["Lunedì", "Martedì", "Mercoledì", "Giovedì", "Venerdì", "Sabato", "Domenica"][date.weekday()]
        self.date_label.setText(f"{weekday} {date_str}")

        if not self.match_calendar:
            return

        # Ottieni info partite
        date_info = self.match_calendar.get_match_density_for_date(date)
        matches = date_info.get("matches", [])

        # Pulisci lista
        self.matches_list.clear()

        # Popola lista partite
        if matches:
            for match in matches:
                item_text = f"{match.time} - {match.home_team} vs {match.away_team}"
                if match.played:
                    item_text += f" ({match.result_string})"
                else:
                    item_text += " (da giocare)"

                item = QListWidgetItem(item_text)

                # Colora in base allo stato
                if match.played:
                    item.setBackground(QColor(240, 240, 240))
                else:
                    item.setBackground(QColor(255, 255, 255))

                # Colore testo per competizione
                if "UEFA" in match.competition:
                    item.setForeground(QColor(184, 134, 11))
                elif "Coppa" in match.competition:
                    item.setForeground(QColor(25, 25, 112))
                elif "Serie A" in match.competition:
                    item.setForeground(QColor(0, 100, 0))

                self.matches_list.addItem(item)

            # Aggiorna info competizioni
            competitions = date_info["competitions"]
            comp_text = f"Competizioni: {', '.join(competitions)}\nPartite totali: {date_info['count']}"
            self.competitions_info.setText(comp_text)
        else:
            # Nessuna partita
            item = QListWidgetItem("Nessuna partita programmata")
            item.setForeground(QColor(128, 128, 128))
            self.matches_list.addItem(item)
            self.competitions_info.setText("Giorno libero")

class AdvancedCalendarWidget(QDialog):
    """Widget calendario avanzato principale"""

    # Segnali
    date_selected = pyqtSignal(datetime.date)  # Data selezionata per simulazione
    simulation_requested = pyqtSignal(datetime.date)  # Richiesta simulazione fino a data

    def __init__(self, parent=None):
        super().__init__(parent)
        self.match_calendar = None
        self.competition_manager = None
        self.season = None

        # Stato simulazione
        self.is_simulating = False
        self.simulation_timer = QTimer()
        self.simulation_timer.timeout.connect(self.update_simulation_progress)

        self.setup_ui()
        self.setWindowTitle("📅 Calendario Stagionale - Navigazione Temporale")
        self.resize(1000, 700)

    def setup_ui(self):
        """Setup interfaccia principale"""
        layout = QHBoxLayout(self)

        # Splitter principale
        splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(splitter)

        # Panel sinistro: Calendario
        calendar_widget = QWidget()
        calendar_layout = QVBoxLayout(calendar_widget)

        # Titolo calendario
        calendar_title = QLabel("Calendario Stagionale")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        calendar_title.setFont(title_font)
        calendar_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        calendar_layout.addWidget(calendar_title)

        # Calendario personalizzato
        self.calendar = CustomCalendarWidget()
        self.calendar.clicked[QDate].connect(self.on_date_selected)
        self.calendar.currentPageChanged.connect(self.on_month_changed)
        calendar_layout.addWidget(self.calendar)

        # Controlli navigazione rapida
        nav_group = QGroupBox("Navigazione Rapida")
        nav_layout = QHBoxLayout(nav_group)

        self.next_match_btn = QPushButton("Prossima Partita")
        self.next_match_btn.clicked.connect(self.go_to_next_match)
        nav_layout.addWidget(self.next_match_btn)

        self.month_end_btn = QPushButton("Fine Mese")
        self.month_end_btn.clicked.connect(self.go_to_month_end)
        nav_layout.addWidget(self.month_end_btn)

        calendar_layout.addWidget(nav_group)

        # Controlli simulazione
        sim_group = QGroupBox("Simulazione")
        sim_layout = QVBoxLayout(sim_group)

        buttons_layout = QHBoxLayout()

        # Nota: Simulazione gestita dal GameEngine centrale
        info_note = QLabel("ℹ️ Usa il pulsante 'Continue' principale per avanzare nel tempo")
        info_note.setStyleSheet("color: #666; font-style: italic; padding: 8px;")
        info_note.setWordWrap(True)
        buttons_layout.addWidget(info_note)

        self.cancel_btn = QPushButton("Annulla")
        self.cancel_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLOR_ERROR};
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 4px;
            }}
            QPushButton:hover {{
                background-color: #c82333;
            }}
        """)
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)

        sim_layout.addLayout(buttons_layout)

        # Progress bar simulazione
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        sim_layout.addWidget(self.progress_bar)

        self.progress_label = QLabel("")
        self.progress_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.progress_label.setVisible(False)
        sim_layout.addWidget(self.progress_label)

        calendar_layout.addWidget(sim_group)

        splitter.addWidget(calendar_widget)

        # Panel destro: Anteprima partite
        self.match_preview = MatchPreviewWidget()
        splitter.addWidget(self.match_preview)

        # Dimensioni iniziali splitter
        splitter.setSizes([600, 400])

    def load_data(self, season: Season, match_calendar: MatchCalendar, competition_manager: CompetitionManager):
        """Carica i dati della stagione"""
        self.season = season
        self.match_calendar = match_calendar
        self.competition_manager = competition_manager

        # Configura calendario
        self.calendar.set_match_calendar(match_calendar)
        self.match_preview.set_match_calendar(match_calendar)

        # Imposta data corrente
        current_date = season.current_date
        self.calendar.setSelectedDate(QDate(current_date.year, current_date.month, current_date.day))

        # Aggiorna anteprima
        self.on_date_selected(QDate(current_date.year, current_date.month, current_date.day))

        logger.info("Dati caricati nel calendario avanzato")

    def on_date_selected(self, qdate: QDate):
        """Gestisce selezione data"""
        py_date = datetime.date(qdate.year(), qdate.month(), qdate.day())
        self.match_preview.update_preview(py_date)

        # Aggiorna testo bottone simulazione
        if self.season:
            days_diff = (py_date - self.season.current_date).days
            if days_diff > 0:
                self.simulate_btn.setText(f"Simula fino al {py_date.strftime('%d/%m')} ({days_diff} giorni)")
                self.simulate_btn.setEnabled(True)
            elif days_diff == 0:
                self.simulate_btn.setText("Data corrente")
                self.simulate_btn.setEnabled(False)
            else:
                self.simulate_btn.setText("Data passata")
                self.simulate_btn.setEnabled(False)

    def on_month_changed(self):
        """Gestisce cambio mese nel calendario"""
        self.calendar.refresh_match_highlights()

    def go_to_next_match(self):
        """Va alla prossima partita"""
        if not self.match_calendar:
            return

        next_event = self.match_calendar.get_next_important_event()
        if next_event:
            next_date = next_event["date"]
            qdate = QDate(next_date.year, next_date.month, next_date.day)
            self.calendar.setSelectedDate(qdate)
            self.on_date_selected(qdate)

    def go_to_month_end(self):
        """Va a fine mese corrente"""
        current_date = self.calendar.selectedDate()
        import calendar
        last_day = calendar.monthrange(current_date.year(), current_date.month())[1]
        month_end = QDate(current_date.year(), current_date.month(), last_day)
        self.calendar.setSelectedDate(month_end)
        self.on_date_selected(month_end)

    # Metodi di simulazione rimossi - gestiti dal GameEngine centrale

    # Tutti i metodi di simulazione sono stati rimossi
    # La simulazione è ora gestita centralmente dal GameEngine