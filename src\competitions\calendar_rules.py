"""
Sistema di regole dinamiche per generazione calendari
Sostituisce le date hardcoded con regole basate su logica temporale
"""
import datetime
from typing import Dict, List, Optional, Tuple
from enum import Enum
from dataclasses import dataclass

from ..core.utils import logger


class WeekDay(Enum):
    """Giorni della settimana"""
    MONDAY = 0
    TUESDAY = 1
    WEDNESDAY = 2
    THURSDAY = 3
    FRIDAY = 4
    SATURDAY = 5
    SUNDAY = 6


class MonthPeriod(Enum):
    """Periodi del mese"""
    FIRST_WEEK = 1
    SECOND_WEEK = 2
    THIRD_WEEK = 3
    FOURTH_WEEK = 4
    LAST_WEEK = -1


@dataclass
class DateRule:
    """Regola per calcolare una data dinamicamente"""
    month: int
    week_period: MonthPeriod
    weekday: WeekDay
    description: str
    
    def calculate_date(self, year: int) -> datetime.date:
        """Calcola la data basata sulla regola per l'anno specificato"""
        # Primo giorno del mese
        first_day = datetime.date(year, self.month, 1)
        
        if self.week_period == MonthPeriod.LAST_WEEK:
            # Ultimo giorno del mese
            if self.month == 12:
                last_day = datetime.date(year + 1, 1, 1) - datetime.timedelta(days=1)
            else:
                last_day = datetime.date(year, self.month + 1, 1) - datetime.timedelta(days=1)
            
            # Trova l'ultimo giorno della settimana specificato
            days_back = (last_day.weekday() - self.weekday.value) % 7
            target_date = last_day - datetime.timedelta(days=days_back)
        else:
            # Trova il primo giorno della settimana specificato nel mese
            days_ahead = (self.weekday.value - first_day.weekday()) % 7
            first_occurrence = first_day + datetime.timedelta(days=days_ahead)
            
            # Aggiungi settimane per arrivare al periodo desiderato
            weeks_to_add = self.week_period.value - 1
            target_date = first_occurrence + datetime.timedelta(weeks=weeks_to_add)
            
            # Verifica che sia ancora nel mese corretto
            if target_date.month != self.month:
                # Se siamo andati oltre, torna indietro di una settimana
                target_date -= datetime.timedelta(weeks=1)
        
        return target_date


class CalendarRules:
    """Gestisce tutte le regole per generare calendari dinamici"""
    
    def __init__(self):
        self.uefa_rules = self._setup_uefa_rules()
        self.uefa_draw_rules = self._setup_uefa_draw_rules()
        self.coppa_italia_rules = self._setup_coppa_italia_rules()
        self.season_rules = self._setup_season_rules()
        
    def _setup_uefa_rules(self) -> Dict[str, DateRule]:
        """Setup regole per le competizioni UEFA (League Phase + Knockout)"""
        return {
            # === LEAGUE PHASE ===
            # Giornata 1: Terzo martedì di settembre
            "MD1": DateRule(9, MonthPeriod.THIRD_WEEK, WeekDay.TUESDAY, "Terzo martedì di settembre"),

            # Giornata 2: Primo martedì di ottobre
            "MD2": DateRule(10, MonthPeriod.FIRST_WEEK, WeekDay.TUESDAY, "Primo martedì di ottobre"),

            # Giornata 3: Terzo martedì di ottobre
            "MD3": DateRule(10, MonthPeriod.THIRD_WEEK, WeekDay.TUESDAY, "Terzo martedì di ottobre"),

            # Giornata 4: Primo martedì di novembre
            "MD4": DateRule(11, MonthPeriod.FIRST_WEEK, WeekDay.TUESDAY, "Primo martedì di novembre"),

            # Giornata 5: Ultimo martedì di novembre
            "MD5": DateRule(11, MonthPeriod.LAST_WEEK, WeekDay.TUESDAY, "Ultimo martedì di novembre"),

            # Giornata 6: Secondo martedì di dicembre
            "MD6": DateRule(12, MonthPeriod.SECOND_WEEK, WeekDay.TUESDAY, "Secondo martedì di dicembre"),

            # Giornata 7: Terzo martedì di gennaio (anno successivo)
            "MD7": DateRule(1, MonthPeriod.THIRD_WEEK, WeekDay.TUESDAY, "Terzo martedì di gennaio"),

            # Giornata 8: Ultimo martedì di gennaio (anno successivo)
            "MD8": DateRule(1, MonthPeriod.LAST_WEEK, WeekDay.TUESDAY, "Ultimo martedì di gennaio"),

            # === KNOCKOUT PHASE ===
            # Playoff Round (solo per Europa League e Conference League)
            "Playoff_Andata": DateRule(2, MonthPeriod.SECOND_WEEK, WeekDay.THURSDAY, "Secondo giovedì di febbraio"),
            "Playoff_Ritorno": DateRule(2, MonthPeriod.THIRD_WEEK, WeekDay.THURSDAY, "Terzo giovedì di febbraio"),

            # Ottavi di Finale
            "R16_Andata": DateRule(3, MonthPeriod.SECOND_WEEK, WeekDay.TUESDAY, "Secondo martedì di marzo"),
            "R16_Ritorno": DateRule(3, MonthPeriod.THIRD_WEEK, WeekDay.TUESDAY, "Terzo martedì di marzo"),

            # Quarti di Finale
            "QF_Andata": DateRule(4, MonthPeriod.SECOND_WEEK, WeekDay.TUESDAY, "Secondo martedì di aprile"),
            "QF_Ritorno": DateRule(4, MonthPeriod.THIRD_WEEK, WeekDay.TUESDAY, "Terzo martedì di aprile"),

            # Semifinali
            "SF_Andata": DateRule(5, MonthPeriod.FIRST_WEEK, WeekDay.TUESDAY, "Primo martedì di maggio"),
            "SF_Ritorno": DateRule(5, MonthPeriod.SECOND_WEEK, WeekDay.TUESDAY, "Secondo martedì di maggio"),

            # Finali (sempre sabato)
            "Final": DateRule(5, MonthPeriod.LAST_WEEK, WeekDay.SATURDAY, "Ultimo sabato di maggio")
        }
    
    def _setup_coppa_italia_rules(self) -> Dict[str, DateRule]:
        """Setup regole per la Coppa Italia"""
        return {
            "Turno Preliminare": DateRule(8, MonthPeriod.SECOND_WEEK, WeekDay.SATURDAY, 
                                        "Secondo sabato di agosto"),
            
            "Primo Turno": DateRule(8, MonthPeriod.THIRD_WEEK, WeekDay.SATURDAY, 
                                  "Terzo sabato di agosto"),
            
            "Secondo Turno": DateRule(11, MonthPeriod.FIRST_WEEK, WeekDay.WEDNESDAY, 
                                    "Primo mercoledì di novembre"),
            
            "Sedicesimi": DateRule(12, MonthPeriod.FIRST_WEEK, WeekDay.WEDNESDAY, 
                                 "Primo mercoledì di dicembre"),
            
            "Ottavi di Finale": DateRule(1, MonthPeriod.FIRST_WEEK, WeekDay.WEDNESDAY,
                                        "Primo mercoledì di gennaio"),

            "Quarti di Finale": DateRule(1, MonthPeriod.SECOND_WEEK, WeekDay.WEDNESDAY,
                                       "Secondo mercoledì di gennaio"),

            "Semifinali Andata": DateRule(4, MonthPeriod.FIRST_WEEK, WeekDay.WEDNESDAY,
                                        "Primo mercoledì di aprile"),

            "Semifinali Ritorno": DateRule(4, MonthPeriod.THIRD_WEEK, WeekDay.WEDNESDAY,
                                         "Terzo mercoledì di aprile"),

            "Finale": DateRule(5, MonthPeriod.SECOND_WEEK, WeekDay.WEDNESDAY,
                             "Secondo mercoledì di maggio")
        }

    def _setup_uefa_draw_rules(self) -> Dict[str, DateRule]:
        """Setup regole per i sorteggi UEFA"""
        return {
            # Sorteggio League Phase: Ultimo giovedì di agosto
            "League_Phase_Draw": DateRule(8, MonthPeriod.LAST_WEEK, WeekDay.THURSDAY,
                                        "Ultimo giovedì di agosto"),

            # Sorteggio Playoff: Primo venerdì di febbraio
            "Playoff_Draw": DateRule(2, MonthPeriod.FIRST_WEEK, WeekDay.FRIDAY,
                                   "Primo venerdì di febbraio"),

            # Sorteggio Ottavi: Ultimo venerdì di febbraio
            "R16_Draw": DateRule(2, MonthPeriod.LAST_WEEK, WeekDay.FRIDAY,
                               "Ultimo venerdì di febbraio"),

            # Sorteggio Quarti e Semifinali: Primo venerdì di marzo
            "QF_SF_Draw": DateRule(3, MonthPeriod.FIRST_WEEK, WeekDay.FRIDAY,
                                 "Primo venerdì di marzo")
        }

    def _setup_season_rules(self) -> Dict[str, DateRule]:
        """Setup regole per le date chiave della stagione"""
        return {
            # Inizio stagione: 1 luglio
            "season_start": DateRule(7, MonthPeriod.FIRST_WEEK, WeekDay.MONDAY, "1 luglio"),

            # Inizio campionato: Terzo sabato di agosto
            "league_start": DateRule(8, MonthPeriod.THIRD_WEEK, WeekDay.SATURDAY, "Terzo sabato di agosto"),

            # Fine campionato: Primo sabato di giugno (anno successivo) - buffer per completare stagione
            "league_end": DateRule(6, MonthPeriod.FIRST_WEEK, WeekDay.SATURDAY, "Primo sabato di giugno"),

            # Fine stagione: 30 giugno (anno successivo)
            "season_end": DateRule(6, MonthPeriod.LAST_WEEK, WeekDay.SUNDAY, "30 giugno")
        }
    
    def get_uefa_dates(self, start_year: int) -> Dict[str, datetime.date]:
        """Genera le date UEFA per una stagione specifica (League Phase + Knockout)"""
        dates = {}

        logger.info(f"Generate date UEFA per stagione {start_year}/{start_year+1}")

        for phase, rule in self.uefa_rules.items():
            # Determina l'anno corretto basato sulla fase
            if phase.startswith("MD") and int(phase[2:]) >= 7:
                # MD7, MD8 sono nell'anno successivo
                year = start_year + 1
            elif phase in ["MD1", "MD2", "MD3", "MD4", "MD5", "MD6"]:
                # MD1-MD6 sono nell'anno di inizio
                year = start_year
            else:
                # Tutte le fasi knockout sono nell'anno successivo
                year = start_year + 1

            dates[phase] = rule.calculate_date(year)
            logger.debug(f"  {phase}: {dates[phase].strftime('%d/%m/%Y')} ({rule.description})")

        return dates

    def get_uefa_draw_dates(self, start_year: int) -> Dict[str, datetime.date]:
        """Genera le date dei sorteggi UEFA per una stagione specifica"""
        dates = {}

        logger.info(f"Generate date sorteggi UEFA per stagione {start_year}/{start_year+1}")

        for draw_type, rule in self.uefa_draw_rules.items():
            # Sorteggio League Phase nell'anno di inizio, altri nell'anno successivo
            year = start_year if draw_type == "League_Phase_Draw" else start_year + 1
            dates[draw_type] = rule.calculate_date(year)
            logger.debug(f"  {draw_type}: {dates[draw_type].strftime('%d/%m/%Y')} ({rule.description})")

        return dates
    
    def get_coppa_italia_dates(self, start_year: int) -> Dict[str, datetime.date]:
        """Genera le date Coppa Italia per una stagione specifica"""
        dates = {}

        logger.info(f"Generate date Coppa Italia per stagione {start_year}/{start_year+1}")

        for round_name, rule in self.coppa_italia_rules.items():
            # Fasi dall'anno successivo: Ottavi, Quarti, Semifinali, Finale
            next_year_phases = ["Ottavi di Finale", "Quarti di Finale", "Semifinali Andata",
                              "Semifinali Ritorno", "Finale"]
            year = start_year + 1 if round_name in next_year_phases else start_year
            dates[round_name] = rule.calculate_date(year)
            logger.debug(f"  {round_name}: {dates[round_name].strftime('%d/%m/%Y')} ({rule.description})")

        return dates
    
    def get_season_key_dates(self, start_year: int) -> Dict[str, datetime.date]:
        """Genera date chiave della stagione basate su regole"""
        dates = {}

        for key, rule in self.season_rules.items():
            # Determina l'anno corretto
            if key in ["league_end", "season_end"]:
                year = start_year + 1
            else:
                year = start_year

            if key == "season_start":
                # Inizio stagione sempre 1 luglio
                dates[key] = datetime.date(year, 7, 1)
            elif key == "season_end":
                # Fine stagione sempre 30 giugno
                dates[key] = datetime.date(year, 6, 30)
            else:
                dates[key] = rule.calculate_date(year)

        # Aggiungi date aggiuntive non basate su regole
        dates.update({
            # Pausa invernale: 20 dicembre
            "winter_break_start": datetime.date(start_year, 12, 20),

            # Fine pausa invernale: 15 gennaio
            "winter_break_end": datetime.date(start_year + 1, 1, 15),

            # Finestre di mercato
            "summer_window_start": datetime.date(start_year, 7, 1),
            "summer_window_end": datetime.date(start_year, 8, 31),
            "winter_window_start": datetime.date(start_year + 1, 1, 2),
            "winter_window_end": datetime.date(start_year + 1, 1, 31)
        })

        return dates


# Istanza globale delle regole
calendar_rules = CalendarRules()
