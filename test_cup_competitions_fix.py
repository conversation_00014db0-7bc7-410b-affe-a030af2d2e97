"""
Test per verificare la correzione del bug delle coppe nazionali.
Bug: Le coppe nazionali (Coppa Italia, etc.) non vengono mai giocate.
"""

import datetime
import sys
import os

# Aggiungi il percorso src al path per importare i moduli
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.core.game_engine import GameEngine
from src.competitions.season import Season
from src.competitions.calendar import generate_full_season_calendar
from src.competitions.competition import setup_italian_competitions
from src.data.data_loader import DataLoader


def test_cup_competitions_generation():
    """Test che verifica che le coppe nazionali vengano generate e aggiunte al calendario"""
    
    print("=== TEST: Generazione Coppe Nazionali ===")
    
    # Setup base del gioco
    data_loader = DataLoader()
    data_loader.load_all_data()
    
    # Setup squadre per livello
    teams_by_level = {
        1: [team["nome"] for team in data_loader.get_serie_a_teams()],
        2: [team["nome"] for team in data_loader.get_serie_b_teams()]
    }
    
    # Crea stagione
    season = Season(2024)
    
    # Setup competizioni
    serie_c_groups = data_loader.get_serie_c_groups()
    competition_manager = setup_italian_competitions(season, teams_by_level, serie_c_groups)
    
    print(f"Competizioni create: {list(competition_manager.competitions.keys())}")
    
    # Verifica che le coppe siano state create
    cups = competition_manager.get_cups_only()
    cup_names = [cup.name for cup in cups]
    print(f"Coppe trovate: {cup_names}")
    
    # Genera calendario completo
    print("\n=== GENERAZIONE CALENDARIO ===")
    calendar = generate_full_season_calendar(season, competition_manager)
    
    # Analizza il calendario generato
    summary = calendar.get_calendar_summary()
    print(f"Partite totali programmate: {summary['total_matches']}")
    print(f"Competizioni nel calendario: {summary['competitions']}")
    
    # Verifica partite di coppa
    print("\n=== VERIFICA PARTITE DI COPPA ===")
    
    cup_matches_found = {}
    all_matches = calendar.matches
    
    for match in all_matches:
        comp_name = match.competition
        if any(cup_keyword in comp_name.lower() for cup_keyword in ['coppa', 'supercoppa']):
            if comp_name not in cup_matches_found:
                cup_matches_found[comp_name] = []
            cup_matches_found[comp_name].append(match)
    
    print(f"Partite di coppa trovate nel calendario:")
    total_cup_matches = 0
    for comp_name, matches in cup_matches_found.items():
        print(f"  {comp_name}: {len(matches)} partite")
        total_cup_matches += len(matches)
        
        # Mostra alcune partite di esempio
        if len(matches) > 0:
            example_match = matches[0]
            print(f"    Esempio: {example_match.home_team} vs {example_match.away_team} - {example_match.date}")
    
    print(f"\nTotale partite di coppa: {total_cup_matches}")
    
    # Verifica approccio dinamico
    print(f"\n=== VERIFICA APPROCCIO DINAMICO ===")

    # Controlla che siano stati generati SOLO i primi turni
    preliminary_matches = [m for m in all_matches if "Preliminare" in m.competition]
    first_round_matches = [m for m in all_matches if "Primo Turno" in m.competition]
    second_round_matches = [m for m in all_matches if "Secondo Turno" in m.competition]

    print(f"Turno Preliminare: {len(preliminary_matches)} partite")
    print(f"Primo Turno: {len(first_round_matches)} partite")
    print(f"Secondo Turno: {len(second_round_matches)} partite (dovrebbe essere 0)")

    # Test risultato
    if total_cup_matches > 0 and len(second_round_matches) == 0:
        print("\n✅ TEST SUPERATO: Le coppe nazionali sono state generate con approccio dinamico!")
        print("✓ Solo i primi turni sono stati generati")
        print("✓ I turni successivi verranno generati dopo i risultati")
        return True
    else:
        print("\n❌ TEST FALLITO: Problema con l'approccio dinamico!")
        return False


def test_cup_matches_simulation():
    """Test che verifica che le partite di coppa vengano simulate dal GameEngine"""
    
    print("\n=== TEST: Simulazione Partite di Coppa ===")
    
    # Setup base del gioco
    data_loader = DataLoader()
    data_loader.load_all_data()
    
    # Setup squadre per livello
    teams_by_level = {
        1: [team["nome"] for team in data_loader.get_serie_a_teams()],
        2: [team["nome"] for team in data_loader.get_serie_b_teams()]
    }
    
    # Crea stagione
    season = Season(2024)
    
    # Setup competizioni
    serie_c_groups = data_loader.get_serie_c_groups()
    competition_manager = setup_italian_competitions(season, teams_by_level, serie_c_groups)
    
    # Genera calendario
    calendar = generate_full_season_calendar(season, competition_manager)
    
    # Trova partite di coppa programmate per le prime settimane
    early_cup_matches = []
    cutoff_date = season.current_date + datetime.timedelta(days=60)  # Prime 8 settimane
    
    for match in calendar.matches:
        if (match.date <= cutoff_date and 
            any(cup_keyword in match.competition.lower() for cup_keyword in ['coppa', 'supercoppa'])):
            early_cup_matches.append(match)
    
    print(f"Partite di coppa nelle prime 8 settimane: {len(early_cup_matches)}")
    
    if len(early_cup_matches) == 0:
        print("⚠️  Nessuna partita di coppa nelle prime settimane - test non applicabile")
        return True
    
    # Mostra alcune partite di coppa programmate
    print("Partite di coppa programmate:")
    for i, match in enumerate(early_cup_matches[:5]):  # Prime 5
        print(f"  {i+1}. {match.competition}: {match.home_team} vs {match.away_team} - {match.date}")
    
    # Crea GameEngine
    game_engine = GameEngine(
        season=season,
        competition_manager=competition_manager,
        calendar=calendar,
        player_team="Juventus"
    )
    
    print(f"\nGameEngine inizializzato con {len(game_engine.stop_points)} stop points")
    
    # Verifica che ci siano stop points per partite di coppa
    cup_stop_points = []
    for stop_point in game_engine.stop_points:
        if (hasattr(stop_point, 'data') and stop_point.data and 
            'match' in stop_point.data):
            match = stop_point.data['match']
            if any(cup_keyword in match.competition.lower() for cup_keyword in ['coppa', 'supercoppa']):
                cup_stop_points.append(stop_point)
    
    print(f"Stop points per partite di coppa: {len(cup_stop_points)}")
    
    if len(cup_stop_points) > 0:
        example_stop = cup_stop_points[0]
        if hasattr(example_stop, 'data') and example_stop.data and 'match' in example_stop.data:
            match = example_stop.data['match']
            print(f"Esempio stop point: {match.competition} - {match.home_team} vs {match.away_team} ({example_stop.date})")
    
    # Test risultato
    if len(early_cup_matches) > 0:
        print("\n✅ TEST SUPERATO: Le partite di coppa sono presenti nel calendario e possono essere simulate!")
        return True
    else:
        print("\n❌ TEST FALLITO: Le partite di coppa non sono state trovate!")
        return False


def test_dynamic_cup_progression():
    """Test che verifica la generazione dinamica dei turni di coppa"""

    print("\n=== TEST: Generazione Dinamica Turni di Coppa ===")

    # Setup base del gioco
    data_loader = DataLoader()
    data_loader.load_all_data()

    # Setup squadre per livello
    teams_by_level = {
        1: [team["nome"] for team in data_loader.get_serie_a_teams()],
        2: [team["nome"] for team in data_loader.get_serie_b_teams()]
    }

    # Crea stagione
    season = Season(2024)

    # Setup competizioni
    serie_c_groups = data_loader.get_serie_c_groups()
    competition_manager = setup_italian_competitions(season, teams_by_level, serie_c_groups)

    # Genera calendario
    calendar = generate_full_season_calendar(season, competition_manager)

    # Verifica stato iniziale
    initial_matches = len(calendar.matches)
    coppa_matches_initial = [m for m in calendar.matches if "Coppa Italia" in m.competition and "Serie C" not in m.competition]

    print(f"Partite iniziali nel calendario: {initial_matches}")
    print(f"Partite Coppa Italia iniziali: {len(coppa_matches_initial)}")

    # Mostra i turni iniziali
    turni_iniziali = {}
    for match in coppa_matches_initial:
        turno = match.competition.split(" - ")[-1] if " - " in match.competition else "Sconosciuto"
        turni_iniziali[turno] = turni_iniziali.get(turno, 0) + 1

    print("Turni generati inizialmente:")
    for turno, count in turni_iniziali.items():
        print(f"  {turno}: {count} partite")

    # Simula alcune partite del primo turno per testare la generazione dinamica
    print(f"\n=== SIMULAZIONE PARTITE PRIMO TURNO ===")

    # Trova partite del primo turno
    primo_turno_matches = [m for m in coppa_matches_initial if "Primo Turno" in m.competition]

    if len(primo_turno_matches) > 0:
        print(f"Trovate {len(primo_turno_matches)} partite del Primo Turno")

        # Simula manualmente alcune partite per testare
        from src.competitions.calendar import generate_next_cup_round

        # Simula risultati (marca le partite come giocate con risultati casuali)
        import random
        simulated_matches = []

        for i, match in enumerate(primo_turno_matches[:4]):  # Simula solo le prime 4
            match.played = True
            match.home_score = random.randint(0, 3)
            match.away_score = random.randint(0, 3)

            # Assicurati che ci sia un vincitore (no pareggi per semplicità)
            if match.home_score == match.away_score:
                match.home_score += 1

            simulated_matches.append(match)
            print(f"  Simulata: {match.home_team} {match.home_score}-{match.away_score} {match.away_team}")

        # Testa la generazione del turno successivo
        coppa_italia = competition_manager.get_competition("Coppa Italia")
        if coppa_italia:
            next_round_matches = generate_next_cup_round(coppa_italia, simulated_matches, season, calendar)

            if next_round_matches:
                print(f"\n✅ Generazione dinamica FUNZIONA!")
                print(f"Generato prossimo turno con {len(next_round_matches)} partite")

                for match in next_round_matches[:3]:  # Mostra prime 3
                    print(f"  Prossimo turno: {match.home_team} vs {match.away_team} - {match.date}")

                return True
            else:
                print(f"\n❌ Generazione dinamica FALLITA: Nessuna partita generata")
                return False
        else:
            print(f"\n❌ Coppa Italia non trovata")
            return False
    else:
        print(f"\n⚠️  Nessuna partita del Primo Turno trovata per il test")
        return True  # Non è un fallimento, semplicemente non c'è nulla da testare


if __name__ == "__main__":
    print("Avvio test per la correzione del bug delle coppe nazionali...")
    
    # Esegui i test
    test1_passed = test_cup_competitions_generation()
    test2_passed = test_cup_matches_simulation()
    test3_passed = test_dynamic_cup_progression()

    print("\n" + "="*60)
    print("RIEPILOGO RISULTATI TEST:")
    print(f"Test generazione coppe: {'✅ SUPERATO' if test1_passed else '❌ FALLITO'}")
    print(f"Test simulazione coppe: {'✅ SUPERATO' if test2_passed else '❌ FALLITO'}")
    print(f"Test generazione dinamica: {'✅ SUPERATO' if test3_passed else '❌ FALLITO'}")

    if test1_passed and test2_passed and test3_passed:
        print("\n🎉 TUTTI I TEST SUPERATI! Il sistema dinamico delle coppe funziona perfettamente!")
        print("✓ Solo i primi turni vengono generati inizialmente")
        print("✓ I turni successivi vengono generati dinamicamente dopo i risultati")
        print("✓ Il sistema rispetta il regolamento reale delle coppe")
    else:
        print("\n⚠️  ALCUNI TEST FALLITI. Verificare la correzione.")
