"""
Modello Season per gestione stagioni calcistiche
"""
import datetime
from typing import Dict, List, Optional
from enum import Enum
from dataclasses import dataclass, field

from ..core.utils import logger, get_season_string
from .calendar_rules import calendar_rules

class SeasonPhase(Enum):
    """Fasi della stagione"""
    PREPARAZIONE = "Preparazione"           # Luglio - Agosto
    PRIMA_PARTE = "Prima Parte Stagione"   # Settembre - Dicembre
    PAUSA_INVERNALE = "Pausa Invernale"    # Gennaio
    SECONDA_PARTE = "Seconda Parte"        # Febbraio - Maggio
    PLAYOFF = "Playoff/Playout"            # Maggio - Giugno
    FINE_STAGIONE = "Fine Stagione"        # Giugno - Luglio

class TransferWindow(Enum):
    """Finestre di mercato"""
    SUMMER = "Mercato Estivo"    # Luglio - Agosto
    WINTER = "Mercato Invernale" # Gennaio
    CLOSED = "Mercato Chiuso"

@dataclass
class SeasonDates:
    """Date importanti della stagione - generate dinamicamente"""
    season_start: datetime.date
    league_start: datetime.date
    winter_break_start: datetime.date
    winter_break_end: datetime.date
    league_end: datetime.date
    season_end: datetime.date

    # Finestre di mercato
    summer_window_start: datetime.date
    summer_window_end: datetime.date
    winter_window_start: datetime.date
    winter_window_end: datetime.date

    @classmethod
    def create_for_season(cls, start_year: int) -> 'SeasonDates':
        """Crea date stagione usando regole dinamiche"""
        key_dates = calendar_rules.get_season_key_dates(start_year)
        return cls(**key_dates)

class Season:
    """Classe principale per gestire una stagione calcistica"""

    def __init__(self, start_year: int = 2025):
        self.start_year = start_year
        self.end_year = start_year + 1
        self.season_name = get_season_string(start_year)

        # Date della stagione (generate dinamicamente)
        self.dates = SeasonDates.create_for_season(start_year)

        # Stato stagione
        self.current_date = self.dates.season_start
        self.current_phase = self.get_phase_for_date(self.current_date)
        self.current_matchday = 0
        self.max_matchdays = 38  # Per Serie A/B, Serie C ha diversi

        # Competizioni attive
        self.active_competitions: Dict[str, object] = {}

        # Statistiche stagione
        self.statistics = {
            "matches_played": 0,
            "total_goals": 0,
            "total_cards": 0,
            "total_attendances": 0
        }

        logger.info(f"Creata stagione {self.season_name}")

    @property
    def is_transfer_window_open(self) -> bool:
        """Verifica se finestra di mercato è aperta"""
        return self.get_transfer_window() != TransferWindow.CLOSED

    @property
    def transfer_window_days_left(self) -> int:
        """Giorni rimasti nella finestra di mercato"""
        window = self.get_transfer_window()
        if window == TransferWindow.CLOSED:
            return 0

        if window == TransferWindow.SUMMER:
            return (self.dates.summer_window_end - self.current_date).days
        elif window == TransferWindow.WINTER:
            return (self.dates.winter_window_end - self.current_date).days

        return 0

    @property
    def days_until_season_start(self) -> int:
        """Giorni fino all'inizio stagione"""
        if self.current_date >= self.dates.league_start:
            return 0
        return (self.dates.league_start - self.current_date).days

    @property
    def days_until_season_end(self) -> int:
        """Giorni fino alla fine stagione"""
        if self.current_date >= self.dates.season_end:
            return 0
        return (self.dates.season_end - self.current_date).days

    @property
    def season_progress_percentage(self) -> float:
        """Percentuale completamento stagione"""
        total_days = (self.dates.season_end - self.dates.season_start).days
        elapsed_days = (self.current_date - self.dates.season_start).days
        return min(100.0, max(0.0, (elapsed_days / total_days) * 100))

    def get_phase_for_date(self, date: datetime.date) -> SeasonPhase:
        """Determina fase stagione per data specifica"""
        if date < self.dates.league_start:
            return SeasonPhase.PREPARAZIONE
        elif date < self.dates.winter_break_start:
            return SeasonPhase.PRIMA_PARTE
        elif date < self.dates.winter_break_end:
            return SeasonPhase.PAUSA_INVERNALE
        elif date < self.dates.league_end:
            return SeasonPhase.SECONDA_PARTE
        elif date < self.dates.season_end:
            return SeasonPhase.PLAYOFF
        else:
            return SeasonPhase.FINE_STAGIONE

    def get_transfer_window(self) -> TransferWindow:
        """Determina finestra di mercato attuale"""
        if (self.dates.summer_window_start <= self.current_date <= self.dates.summer_window_end):
            return TransferWindow.SUMMER
        elif (self.dates.winter_window_start <= self.current_date <= self.dates.winter_window_end):
            return TransferWindow.WINTER
        else:
            return TransferWindow.CLOSED

    def advance_date(self, days: int = 1) -> List[str]:
        """Avanza data di X giorni e restituisce eventi"""
        events = []
        old_phase = self.current_phase

        for day in range(days):
            self.current_date += datetime.timedelta(days=1)

            # Controlla cambi di fase
            new_phase = self.get_phase_for_date(self.current_date)
            if new_phase != self.current_phase:
                self.current_phase = new_phase
                events.append(f"Iniziata fase: {new_phase.value}")

            # Controlla apertura/chiusura mercato
            transfer_window = self.get_transfer_window()
            if day == 0:  # Solo primo giorno
                if (self.current_date == self.dates.summer_window_start):
                    events.append("Aperto mercato estivo")
                elif (self.current_date == self.dates.summer_window_end + datetime.timedelta(days=1)):
                    events.append("Chiuso mercato estivo")
                elif (self.current_date == self.dates.winter_window_start):
                    events.append("Aperto mercato invernale")
                elif (self.current_date == self.dates.winter_window_end + datetime.timedelta(days=1)):
                    events.append("Chiuso mercato invernale")

            # Altri eventi stagionali
            if self.current_date == self.dates.league_start:
                events.append("Iniziato campionato ufficiale")
            elif self.current_date == self.dates.league_end:
                events.append("Terminato campionato")
            elif self.current_date == self.dates.season_end:
                events.append("Fine stagione ufficiale")

        return events

    def get_next_important_date(self) -> tuple[datetime.date, str]:
        """Restituisce prossima data importante"""
        important_dates = [
            (self.dates.league_start, "Inizio campionato"),
            (self.dates.summer_window_end, "Chiusura mercato estivo"),
            (self.dates.winter_break_start, "Inizio pausa invernale"),
            (self.dates.winter_window_start, "Apertura mercato invernale"),
            (self.dates.winter_window_end, "Chiusura mercato invernale"),
            (self.dates.winter_break_end, "Fine pausa invernale"),
            (self.dates.league_end, "Fine campionato"),
            (self.dates.season_end, "Fine stagione")
        ]

        for date, description in important_dates:
            if date > self.current_date:
                return date, description

        # Se non ci sono più date, prossima stagione
        next_season_start = datetime.date(self.end_year, 7, 1)
        return next_season_start, "Inizio prossima stagione"

    def is_match_day_possible(self, date: datetime.date = None) -> bool:
        """Verifica se è possibile giocare partite in una data"""
        check_date = date or self.current_date

        # Non si gioca durante la pausa invernale
        if (self.dates.winter_break_start <= check_date <= self.dates.winter_break_end):
            return False

        # Non si gioca prima dell'inizio campionato
        if check_date < self.dates.league_start:
            return False

        # Non si gioca dopo la fine campionato (tranne playoff)
        if check_date > self.dates.league_end and self.current_phase != SeasonPhase.PLAYOFF:
            return False

        # Tipicamente si gioca nei weekend (sabato=5, domenica=6) e mercoledì (2)
        weekday = check_date.weekday()
        return weekday in [2, 5, 6]  # Mercoledì, Sabato, Domenica

    def is_league_match_day_possible(self, date: datetime.date, calendar=None) -> bool:
        """Verifica se è possibile giocare partite di campionato in una data"""
        if not self.is_match_day_possible(date):
            return False

        # Controlla se ci sono già partite di coppa in quella data
        if calendar:
            matches_on_date = calendar.get_matches_for_date(date)
            for match in matches_on_date:
                if "coppa" in match.competition.lower() or "uefa" in match.competition.lower():
                    return False # Giorno occupato da coppe

        return True

    def get_season_summary(self) -> Dict:
        """Restituisce riassunto stagione"""
        next_date, next_event = self.get_next_important_date()

        return {
            "season_name": self.season_name,
            "current_date": self.current_date,
            "current_phase": self.current_phase.value,
            "current_matchday": self.current_matchday,
            "max_matchdays": self.max_matchdays,
            "progress_percentage": self.season_progress_percentage,
            "transfer_window": self.get_transfer_window().value,
            "transfer_window_open": self.is_transfer_window_open,
            "transfer_window_days_left": self.transfer_window_days_left,
            "next_important_date": next_date,
            "next_important_event": next_event,
            "days_to_next_event": (next_date - self.current_date).days,
            "statistics": self.statistics.copy()
        }

    def register_competition(self, competition_name: str, competition_obj):
        """Registra una competizione attiva"""
        self.active_competitions[competition_name] = competition_obj
        logger.info(f"Registrata competizione: {competition_name}")

    def get_competitions_for_matchday(self, matchday: int) -> List[str]:
        """Restituisce competizioni che giocano in una giornata"""
        active_comps = []
        for name, comp in self.active_competitions.items():
            if hasattr(comp, 'has_match_on_matchday') and comp.has_match_on_matchday(matchday):
                active_comps.append(name)
        return active_comps

    def simulate_matchday(self) -> Dict:
        """Simula una giornata di partite"""
        if not self.is_match_day_possible():
            return {"error": "Impossibile giocare partite oggi"}

        if self.current_matchday >= self.max_matchdays:
            return {"error": "Stagione terminata"}

        self.current_matchday += 1
        matches_results = []

        # Simula partite di tutte le competizioni attive
        for comp_name, competition in self.active_competitions.items():
            if hasattr(competition, 'simulate_matchday'):
                comp_results = competition.simulate_matchday(self.current_matchday)
                if comp_results:
                    matches_results.extend(comp_results)

        # Aggiorna statistiche
        self.statistics["matches_played"] += len(matches_results)

        return {
            "matchday": self.current_matchday,
            "date": self.current_date,
            "matches": matches_results,
            "competitions": list(self.active_competitions.keys())
        }

    def end_season(self) -> Dict:
        """Conclude la stagione"""
        logger.info(f"Conclusa stagione {self.season_name}")

        # Calcola statistiche finali
        final_stats = self.get_season_summary()

        # Pulisce competizioni
        self.active_competitions.clear()

        return {
            "season_completed": self.season_name,
            "final_stats": final_stats,
            "next_season": get_season_string(self.start_year + 1)
        }

    def create_next_season(self):
        """Crea la stagione successiva"""
        return Season(self.start_year + 1)

    def get_formatted_date(self) -> str:
        """Restituisce data formattata in italiano"""
        from ..core.utils import format_date_italian
        return format_date_italian(self.current_date)

    def to_dict(self) -> Dict:
        """Converte stagione in dizionario per serializzazione"""
        return {
            "start_year": self.start_year,
            "end_year": self.end_year,
            "season_name": self.season_name,
            "current_date": self.current_date.isoformat(),
            "current_phase": self.current_phase.value,
            "current_matchday": self.current_matchday,
            "max_matchdays": self.max_matchdays,
            "statistics": self.statistics
        }

    @classmethod
    def from_dict(cls, data: Dict):
        """Crea stagione da dizionario"""
        season = cls(data["start_year"])
        season.current_date = datetime.date.fromisoformat(data["current_date"])
        season.current_phase = SeasonPhase(data["current_phase"])
        season.current_matchday = data["current_matchday"]
        season.max_matchdays = data["max_matchdays"]
        season.statistics = data["statistics"]
        return season

    def __str__(self) -> str:
        """Rappresentazione stringa della stagione"""
        return f"Stagione {self.season_name} - {self.current_phase.value} (Giornata {self.current_matchday})"