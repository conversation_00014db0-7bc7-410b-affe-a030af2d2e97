"""
Widget tabella partite
"""
from PyQt6.QtWidgets import QTableWidget, QTableWidgetItem, QHeaderView
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont
from typing import List
from src.competitions.calendar import Match
from ..dialogs.match_details_dialog import MatchDetailsDialog


class MatchesTableWidget(QTableWidget):
    """Tabella partite"""
    
    match_selected = pyqtSignal(str)  # Segnale per squadra selezionata

    def __init__(self, parent=None):
        super().__init__(parent)
        self.matches_data: List[Match] = []
        self.setup_table()
        self.setup_connections()

    def setup_table(self):
        """Configura tabella partite"""
        headers = ["Data", "Ora", "Casa", "", "Trasferta", "Comp", "Spettatori"]
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)

        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)

        # Ridimensiona colonne
        header = self.horizontalHeader()
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)  # Casa
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Stretch)  # Trasferta

    def setup_connections(self):
        """Configura connessioni segnali"""
        self.cellDoubleClicked.connect(self.show_match_details)

    def load_matches(self, matches: List[Match]):
        """Carica partite"""
        self.matches_data = matches  # Store for details dialog
        self.setRowCount(len(matches))
        
        for row, match in enumerate(matches):
            # Data
            date_item = QTableWidgetItem(match.date.strftime("%d/%m"))
            date_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.setItem(row, 0, date_item)
            
            # Ora
            time_item = QTableWidgetItem(str(match.time))
            time_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.setItem(row, 1, time_item)
            
            # Squadra casa
            home_item = QTableWidgetItem(match.home_team)
            self.setItem(row, 2, home_item)
            
            # Risultato
            result_item = QTableWidgetItem(match.result_string)
            result_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            if match.played:
                result_item.setFont(QFont("Arial", 9, QFont.Weight.Bold))
            self.setItem(row, 3, result_item)
            
            # Squadra trasferta
            away_item = QTableWidgetItem(match.away_team)
            self.setItem(row, 4, away_item)
            
            # Competizione
            comp_item = QTableWidgetItem(match.competition)
            comp_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.setItem(row, 5, comp_item)
            
            # Spettatori
            attendance = getattr(match, 'attendance', 0)
            attendance_text = f"{attendance:,}" if attendance and attendance > 0 else "-"
            attendance_item = QTableWidgetItem(attendance_text)
            attendance_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.setItem(row, 6, attendance_item)

    def show_match_details(self, row: int, column: int):
        """Mostra dettagli partita"""
        if row < len(self.matches_data):
            match = self.matches_data[row]
            dialog = MatchDetailsDialog(match, parent=self)
            dialog.exec()

    def filter_by_team(self, team_name: str):
        """Filtra partite per squadra"""
        if not team_name or team_name == "Tutte le squadre":
            # Mostra tutte le righe
            for row in range(self.rowCount()):
                self.setRowHidden(row, False)
        else:
            # Nascondi righe che non contengono la squadra
            for row in range(self.rowCount()):
                home_item = self.item(row, 2)
                away_item = self.item(row, 4)
                
                show_row = False
                if home_item and away_item:
                    show_row = (team_name in home_item.text() or 
                              team_name in away_item.text())
                
                self.setRowHidden(row, not show_row)

    def get_selected_match(self) -> Match:
        """Restituisce partita selezionata"""
        current_row = self.currentRow()
        if 0 <= current_row < len(self.matches_data):
            return self.matches_data[current_row]
        return None
