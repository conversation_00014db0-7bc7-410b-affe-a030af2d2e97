"""
Test dell'interfaccia squadra
"""
import sys
from pathlib import Path

src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

from src.core.config import ensure_directories
from src.data.data_loader import data_loader
from src.ui.squad_ui import SquadUI
from src.players.player_generator import player_generator

def test_squad_ui():
    """Test dell'interfaccia squadra"""
    print("=== TEST INTERFACCIA SQUADRA ===")

    app = QApplication(sys.argv)

    # Carica dati
    ensure_directories()
    if not data_loader.load_all_data():
        print("Errore: impossibile caricare i dati")
        return False

    # Crea interfaccia squadra
    squad_ui = SquadUI()

    # Genera squadra di test
    print("Generando rosa di test...")
    test_squad = player_generator.generate_team_squad(team_level=2, squad_size=25)
    squad_ui.load_squad(test_squad)

    # Mostra finestra
    squad_ui.show()
    squad_ui.resize(1000, 700)
    squad_ui.setWindowTitle("Test Interfaccia Squadra")

    # Chiudi automaticamente dopo 3 secondi
    def close_app():
        squad_ui.close()
        app.quit()

    QTimer.singleShot(3000, close_app)

    print(f"Interfaccia squadra caricata con {len(test_squad)} giocatori")
    print("Finestra aperta per 3 secondi...")

    app.exec()
    return True

if __name__ == "__main__":
    if test_squad_ui():
        print("✅ Test interfaccia squadra completato con successo")
    else:
        print("❌ Test interfaccia squadra fallito")
        sys.exit(1)