"""
Interfaccia utente per competizioni e calendari - Versione modulare
Questo file mantiene la compatibilità con il codice esistente
"""

# Import dalla nuova struttura modulare
from .competitions.competitions_ui import CompetitionsUI
from .competitions.dialogs.match_details_dialog import MatchDetailsDialog
from .competitions.widgets.standings_table import StandingsTableWidget
from .competitions.widgets.matches_table import MatchesTableWidget
from .competitions.widgets.season_info import SeasonInfoWidget
from .competitions.tabs.competition_tab import CompetitionTabWidget

# Re-export per compatibilità
__all__ = [
    'CompetitionsUI',
    'MatchDetailsDialog',
    'StandingsTableWidget', 
    'MatchesTableWidget',
    'SeasonInfoWidget',
    'CompetitionTabWidget'
]
