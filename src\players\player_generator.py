"""
Generatore di giocatori per Football Manager Italiano
"""
import random
import datetime
from typing import List, Dict, Optional, Tuple

from .player import Player, PlayerPosition, PlayerAttributes, FootPreference
from ..data.name_generator import name_generator
from ..core.config import PLAYER_AGE_RANGES, STARTING_BUDGETS
from ..core.utils import logger, weighted_random_choice, clamp

class PlayerGenerator:
    """Classe per generazione giocatori realistici"""

    def __init__(self):
        self.position_distributions = self._get_position_distributions()
        self.age_distributions = self._get_age_distributions()

    def _get_position_distributions(self) -> Dict[PlayerPosition, float]:
        """Distribuzione realistica delle posizioni in una squadra"""
        return {
            PlayerPosition.PORTIERE: 0.12,              # 3 portieri su 25
            PlayerPosition.DIFENSORE_CENTRALE: 0.16,     # 4 difensori centrali
            PlayerPosition.DIFENSORE_SINISTRO: 0.08,     # 2 terzini sinistri
            PlayerPosition.DIFENSORE_DESTRO: 0.08,       # 2 terzini destri
            PlayerPosition.CENTROCAMPISTA_CENTRALE: 0.16, # 4 centrocampisti centrali
            PlayerPosition.CENTROCAMPISTA_SINISTRO: 0.08, # 2 esterni sinistri
            PlayerPosition.CENTROCAMPISTA_DESTRO: 0.08,   # 2 esterni destri
            PlayerPosition.TREQUARTISTA: 0.08,           # 2 trequartisti
            PlayerPosition.ATTACCANTE_SINISTRO: 0.08,     # 2 attaccanti sinistri
            PlayerPosition.ATTACCANTE_DESTRO: 0.08,       # 2 attaccanti destri
            PlayerPosition.CENTRAVANTI: 0.08             # 2 centravanti
        }

    def _get_age_distributions(self) -> Dict[str, float]:
        """Distribuzione realistica delle età"""
        return {
            "giovane": 0.25,    # 16-21 anni
            "adulto": 0.60,     # 22-29 anni
            "veterano": 0.15    # 30-38 anni
        }

    def generate_player(self, position: Optional[PlayerPosition] = None,
                       age_range: Optional[Tuple[int, int]] = None,
                       overall_rating_range: Optional[Tuple[int, int]] = None,
                       nationality: Optional[str] = None) -> Player:
        """Genera un singolo giocatore"""

        # Determina posizione
        if position is None:
            positions = list(self.position_distributions.keys())
            weights = list(self.position_distributions.values())
            position = weighted_random_choice(positions, weights)

        # Genera nome e nazionalità
        if nationality:
            name_result = name_generator.generate_name_from_nation(nationality)
            if name_result:
                first_name, last_name = name_result
                player_nationality = nationality
            else:
                first_name, last_name, player_nationality = name_generator.generate_name_for_position(position.value)
        else:
            first_name, last_name, player_nationality = name_generator.generate_name_for_position(position.value)

        # Determina età
        if age_range:
            age = random.randint(age_range[0], age_range[1])
        else:
            age_categories = list(self.age_distributions.keys())
            age_weights = list(self.age_distributions.values())
            age_category = weighted_random_choice(age_categories, age_weights)

            if age_category == "giovane":
                age = random.randint(16, 21)
            elif age_category == "adulto":
                age = random.randint(22, 29)
            else:  # veterano
                age = random.randint(30, 38)

        # Calcola data di nascita
        birth_year = datetime.date.today().year - age
        birth_month = random.randint(1, 12)
        birth_day = random.randint(1, 28)  # Sicuro per tutti i mesi
        birth_date = datetime.date(birth_year, birth_month, birth_day)

        # Crea giocatore
        player = Player(first_name, last_name, player_nationality, birth_date, position)

        # Genera attributi
        self._generate_attributes(player, overall_rating_range)

        # Genera caratteristiche secondarie
        self._generate_secondary_characteristics(player)

        logger.debug(f"Generato giocatore: {player}")
        return player

    def _generate_attributes(self, player: Player, overall_rating_range: Optional[Tuple[int, int]] = None):
        """Genera attributi realistici per il giocatore"""

        # Determina overall target
        if overall_rating_range:
            target_overall = random.randint(overall_rating_range[0], overall_rating_range[1])
        else:
            target_overall = self._get_realistic_overall_for_age(player.age)

        # Ottieni bonificatori per nazionalità
        nation_bonuses = name_generator.get_nation_player_style(player.nationality)

        # Genera attributi base
        base_attributes = {}

        for attr_name in ["tecnica", "velocita", "forza", "resistenza", "tiro",
                         "passaggio", "dribbling", "difesa", "portiere", "mentalita"]:

            if attr_name == "portiere" and player.position != PlayerPosition.PORTIERE:
                # Non portieri hanno attributo portiere basso
                base_attributes[attr_name] = random.randint(1, 3)
            else:
                # Attributo base casuale
                base_value = random.randint(8, 15)

                # Applica bonus nazionalità
                nation_bonus = nation_bonuses.get(attr_name, 1.0)
                base_value = int(base_value * nation_bonus)

                base_attributes[attr_name] = clamp(base_value, 1, 20)

        # Aggiusta attributi per raggiungere overall target
        self._adjust_attributes_to_target(base_attributes, player.position, target_overall)

        # Applica attributi al giocatore
        for attr_name, value in base_attributes.items():
            setattr(player.attributes, attr_name, value)

        # Calcola potenziale
        player.potential = self._calculate_potential(player, target_overall)

    def _get_realistic_overall_for_age(self, age: int) -> int:
        """Restituisce overall realistico basato su età"""
        if age <= 18:
            return random.randint(8, 14)  # Giovani promesse
        elif age <= 21:
            return random.randint(10, 16)  # Giovani
        elif age <= 25:
            return random.randint(12, 18)  # Prime/picco carriera
        elif age <= 29:
            return random.randint(13, 19)  # Picco carriera
        elif age <= 32:
            return random.randint(12, 17)  # Esperti
        else:
            return random.randint(10, 16)  # Veterani

    def _adjust_attributes_to_target(self, attributes: Dict[str, int],
                                   position: PlayerPosition, target_overall: int):
        """Aggiusta attributi per raggiungere overall target"""
 
        max_iterations = 50
        iteration = 0

        while iteration < max_iterations:
            # Calcola overall attuale
            # Usa la logica centralizzata da PlayerAttributes
            temp_attrs = PlayerAttributes(**attributes)
            current_overall = temp_attrs.get_overall_rating(position)

            if abs(current_overall - target_overall) <= 1:
                break

            # Aggiusta attributi
            if current_overall < target_overall:
                # Aumenta attributi importanti
                position_weights = temp_attrs._get_position_weights(position)
                important_attrs = [attr for attr, weight in position_weights.items() if weight >= 2.0]
                if important_attrs:
                    attr_to_increase = random.choice(important_attrs)
                    if attributes[attr_to_increase] < 20:
                        attributes[attr_to_increase] += 1

            else:
                # Diminuisci attributi meno importanti
                position_weights = temp_attrs._get_position_weights(position)
                less_important_attrs = [attr for attr, weight in position_weights.items() if weight < 2.0]
                if less_important_attrs:
                    attr_to_decrease = random.choice(less_important_attrs)
                    if attributes[attr_to_decrease] > 1:
                        attributes[attr_to_decrease] -= 1

            iteration += 1

    def _calculate_potential(self, player: Player, current_overall: int) -> int:
        """Calcola potenziale del giocatore"""
        age = player.age

        if age <= 18:
            potential_increase = random.randint(3, 8)
        elif age <= 21:
            potential_increase = random.randint(1, 5)
        elif age <= 25:
            potential_increase = random.randint(0, 3)
        else:
            potential_increase = 0

        return clamp(current_overall + potential_increase, current_overall, 20)

    def _generate_secondary_characteristics(self, player: Player):
        """Genera caratteristiche secondarie del giocatore"""

        # Piede preferito
        foot_preferences = [FootPreference.DESTRO, FootPreference.SINISTRO, FootPreference.AMBIDESTRO]
        foot_weights = [0.7, 0.25, 0.05]  # Maggior parte destri
        player.foot_preference = weighted_random_choice(foot_preferences, foot_weights)

        # Posizioni secondarie (alcune posizioni)
        secondary_positions = self._get_possible_secondary_positions(player.position)
        if secondary_positions and random.random() < 0.4:  # 40% chance di posizione secondaria
            player.secondary_positions = [random.choice(secondary_positions)]

        # Morale iniziale
        player.morale = random.randint(60, 85)

        # Fitness iniziale
        player.fitness = random.randint(90, 100)

    def _get_possible_secondary_positions(self, primary_position: PlayerPosition) -> List[PlayerPosition]:
        """Restituisce posizioni secondarie possibili"""
        secondary_map = {
            PlayerPosition.DIFENSORE_CENTRALE: [PlayerPosition.DIFENSORE_SINISTRO, PlayerPosition.DIFENSORE_DESTRO],
            PlayerPosition.DIFENSORE_SINISTRO: [PlayerPosition.CENTROCAMPISTA_SINISTRO, PlayerPosition.DIFENSORE_CENTRALE],
            PlayerPosition.DIFENSORE_DESTRO: [PlayerPosition.CENTROCAMPISTA_DESTRO, PlayerPosition.DIFENSORE_CENTRALE],
            PlayerPosition.CENTROCAMPISTA_CENTRALE: [PlayerPosition.TREQUARTISTA],
            PlayerPosition.CENTROCAMPISTA_SINISTRO: [PlayerPosition.ATTACCANTE_SINISTRO, PlayerPosition.DIFENSORE_SINISTRO],
            PlayerPosition.CENTROCAMPISTA_DESTRO: [PlayerPosition.ATTACCANTE_DESTRO, PlayerPosition.DIFENSORE_DESTRO],
            PlayerPosition.TREQUARTISTA: [PlayerPosition.CENTROCAMPISTA_CENTRALE, PlayerPosition.ATTACCANTE_SINISTRO, PlayerPosition.ATTACCANTE_DESTRO],
            PlayerPosition.ATTACCANTE_SINISTRO: [PlayerPosition.CENTRAVANTI, PlayerPosition.CENTROCAMPISTA_SINISTRO],
            PlayerPosition.ATTACCANTE_DESTRO: [PlayerPosition.CENTRAVANTI, PlayerPosition.CENTROCAMPISTA_DESTRO],
            PlayerPosition.CENTRAVANTI: [PlayerPosition.TREQUARTISTA]
        }

        return secondary_map.get(primary_position, [])

    def generate_team_squad(self, team_level: int = 2, squad_size: int = 25,
                          nationality_preference: Optional[str] = None) -> List[Player]:
        """Genera una rosa completa per una squadra"""

        squad = []

        # Determina range overall basato su livello squadra
        overall_ranges = {
            1: (14, 19),  # Serie A
            2: (10, 16),  # Serie B
            3: (8, 14)    # Serie C
        }

        overall_range = overall_ranges.get(team_level, (10, 16))

        # Genera giocatori per posizione
        positions_needed = []

        # Calcola quanti giocatori per posizione
        for position, percentage in self.position_distributions.items():
            count = max(1, int(squad_size * percentage))
            positions_needed.extend([position] * count)

        # Aggiusta per arrivare esattamente a squad_size
        while len(positions_needed) < squad_size:
            # Aggiungi posizioni più comuni
            common_positions = [PlayerPosition.CENTROCAMPISTA_CENTRALE,
                              PlayerPosition.DIFENSORE_CENTRALE,
                              PlayerPosition.ATTACCANTE_SINISTRO]
            positions_needed.append(random.choice(common_positions))

        while len(positions_needed) > squad_size:
            positions_needed.pop()

        # Genera giocatori
        for position in positions_needed:
            # 70% possibilità di nazionalità preferita se specificata
            use_preferred_nationality = (nationality_preference and
                                       random.random() < 0.7)

            player_nationality = nationality_preference if use_preferred_nationality else None

            player = self.generate_player(
                position=position,
                overall_rating_range=overall_range,
                nationality=player_nationality
            )

            squad.append(player)

        # Assegna numeri maglia
        self._assign_shirt_numbers(squad)

        logger.info(f"Generata rosa di {len(squad)} giocatori (livello {team_level})")
        return squad

    def _assign_shirt_numbers(self, squad: List[Player]):
        """Assegna numeri di maglia alla rosa"""
        available_numbers = list(range(1, 100))
        random.shuffle(available_numbers)

        # Portieri hanno numeri bassi preferibilmente
        goalkeepers = [p for p in squad if p.position == PlayerPosition.PORTIERE]
        for i, gk in enumerate(goalkeepers):
            gk.shirt_number = i + 1

        # Altri giocatori
        other_players = [p for p in squad if p.position != PlayerPosition.PORTIERE]
        number_index = len(goalkeepers)

        for player in other_players:
            player.shirt_number = available_numbers[number_index]
            number_index += 1

# Istanza globale del generatore
player_generator = PlayerGenerator()