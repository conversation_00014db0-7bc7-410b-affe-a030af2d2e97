"""
Widget calendario per visualizzazione date partite
"""
from PyQt6.QtWidgets import (QWidget, QTableWidget, QTableWidgetItem, QVBoxLayout, 
                            QHBoxLayout, QHeaderView, QLabel, QPushButton, 
                            QDateEdit, QComboBox, QSplitter, QGroupBox)
from PyQt6.QtCore import Qt, QDate
from PyQt6.QtGui import QFont
from ..core.config import COLOR_PRIMARY, COLOR_SUCCESS, COLOR_WARNING, COLOR_ERROR
from ..competitions.calendar import MatchCalendar, Match
from typing import List


class CalendarWidget(QTableWidget):
    """Widget calendario per visualizzare le date delle partite"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.matches_data = []  # Store matches for the calendar
        self.setup_table()
    
    def setup_table(self):
        """Configura la tabella del calendario"""
        # Imposta 7 colonne per i giorni della settimana
        self.setColumnCount(7)
        headers = ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>enerd<PERSON>", "Sabato", "Domenica"]
        self.setHorizontalHeaderLabels(headers)
        
        # Imposta 6 righe (approssimativamente 6 settimane)
        self.setRowCount(6)
        
        # Imposta dimensioni fisse per celle
        for i in range(7):
            self.horizontalHeader().setSectionResizeMode(i, QHeaderView.ResizeMode.Fixed)
            self.horizontalHeader().resizeSection(i, 120)
        
        for i in range(6):
            self.verticalHeader().setSectionResizeMode(i, QHeaderView.ResizeMode.Fixed)
            self.verticalHeader().resizeSection(i, 80)
        
        # Imposta stile
        self.setAlternatingRowColors(True)
        self.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.setSelectionMode(QTableWidget.SelectionMode.NoSelection)
    
    def load_matches_for_month(self, year: int, month: int, matches: List[Match]):
        """Carica partite per un mese specifico"""
        self.matches_data = matches
        
        # Pulisci la tabella
        for i in range(6):
            for j in range(7):
                self.setItem(i, j, QTableWidgetItem(""))
        
        # Trova il primo giorno del mese e il giorno della settimana (0 = Lunedì, 6 = Domenica)
        first_day = QDate(year, month, 1)
        first_weekday = first_day.dayOfWeek() - 1  # Qt usa 1-7 (Lun-Dom), convertiamo a 0-6
        
        # Calcola giorni nel mese
        days_in_month = first_day.daysInMonth()
        
        # Popola la tabella
        day_counter = 1
        for week in range(6):
            for day_of_week in range(7):
                if week == 0 and day_of_week < first_weekday:
                    # Prima settimana, giorni del mese precedente
                    continue
                elif day_counter > days_in_month:
                    # Fine del mese, giorni del mese successivo
                    continue
                else:
                    # Giorno del mese corrente
                    date = QDate(year, month, day_counter)
                    cell_text = str(day_counter)
                    
                    # Cerca partite per questa data
                    date_matches = [m for m in matches if m.date == date.toPyDate()]
                    if date_matches:
                        # Aggiungi indicatore di partite
                        cell_text += f"\n({len(date_matches)} partite)"
                        
                        # Colora la cella se ci sono partite
                        for row in range(self.rowCount()):
                            for col in range(self.columnCount()):
                                item = self.item(row, col)
                                if item and item.text().startswith(str(day_counter)):
                                    item.setBackground(Qt.GlobalColor.yellow)
                                    break
                    
                    self.setItem(week, day_of_week, QTableWidgetItem(cell_text))
                    self.item(week, day_of_week).setTextAlignment(Qt.AlignmentFlag.AlignTop | Qt.AlignmentFlag.AlignLeft)
                    day_counter += 1
    
    def set_current_date(self, current_date: QDate):
        """Evidenzia la data corrente"""
        current_year = current_date.year()
        current_month = current_date.month()
        current_day = current_date.day()
        
        # Trova la posizione della data corrente
        first_day = QDate(current_year, current_month, 1)
        first_weekday = first_day.dayOfWeek() - 1
        
        day_counter = 1
        for week in range(6):
            for day_of_week in range(7):
                if week == 0 and day_of_week < first_weekday:
                    continue
                elif day_counter > first_day.daysInMonth():
                    continue
                else:
                    if day_counter == current_day:
                        item = self.item(week, day_of_week)
                        if item:
                            item.setBackground(Qt.GlobalColor.blue)
                            item.setForeground(Qt.GlobalColor.white)
                    day_counter += 1


class MatchCalendarWidget(QWidget):
    """Widget completo per il calendario delle partite"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.calendar = None
        self.setup_ui()
    
    def setup_ui(self):
        """Configura l'interfaccia del calendario"""
        layout = QVBoxLayout(self)
        
        # Header con controlli
        header_layout = QHBoxLayout()
        
        # Titolo
        title_label = QLabel("Calendario Partite")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Controlli data
        self.month_combo = QComboBox()
        months = ["Gennaio", "Febbraio", "Marzo", "Aprile", "Maggio", "Giugno",
                 "Luglio", "Agosto", "Settembre", "Ottobre", "Novembre", "Dicembre"]
        self.month_combo.addItems(months)
        
        self.year_edit = QDateEdit()
        self.year_edit.setDisplayFormat("yyyy")
        self.year_edit.setDate(QDate.currentDate())
        
        self.prev_month_btn = QPushButton("◀")
        self.next_month_btn = QPushButton("▶")
        
        header_layout.addWidget(QLabel("Mese:"))
        header_layout.addWidget(self.month_combo)
        header_layout.addWidget(QLabel("Anno:"))
        header_layout.addWidget(self.year_edit)
        header_layout.addWidget(self.prev_month_btn)
        header_layout.addWidget(self.next_month_btn)
        
        layout.addLayout(header_layout)
        
        # Tabella calendario
        self.calendar_table = CalendarWidget()
        layout.addWidget(self.calendar_table)
        
        # Pulsanti azioni
        actions_layout = QHBoxLayout()
        
        self.today_btn = QPushButton("Vai a Oggi")
        self.today_btn.clicked.connect(self.go_to_today)
        
        self.upcoming_btn = QPushButton("Partite Prossime")
        self.upcoming_btn.clicked.connect(self.show_upcoming_matches)
        
        self.all_btn = QPushButton("Tutte le Partite")
        self.all_btn.clicked.connect(self.show_all_matches)
        
        actions_layout.addWidget(self.today_btn)
        actions_layout.addWidget(self.upcoming_btn)
        actions_layout.addWidget(self.all_btn)
        actions_layout.addStretch()
        
        layout.addLayout(actions_layout)
        
        # Connetti segnali
        self.month_combo.currentTextChanged.connect(self.on_month_changed)
        self.year_edit.dateChanged.connect(self.on_year_changed)
        self.prev_month_btn.clicked.connect(self.go_to_prev_month)
        self.next_month_btn.clicked.connect(self.go_to_next_month)
        
        # Imposta mese corrente
        current_date = QDate.currentDate()
        self.month_combo.setCurrentIndex(current_date.month() - 1)
        self.year_edit.setDate(current_date)
    
    def load_calendar(self, calendar: MatchCalendar):
        """Carica il calendario di gioco"""
        self.calendar = calendar
        self.refresh_display()
    
    def refresh_display(self):
        """Aggiorna la visualizzazione del calendario"""
        if not self.calendar:
            return
        
        current_year = self.year_edit.date().year()
        current_month = self.month_combo.currentIndex() + 1
        
        # Ottieni tutte le partite per il mese specifico
        matches_in_month = []
        for match in self.calendar.matches:
            if match.date.year == current_year and match.date.month == current_month:
                matches_in_month.append(match)
        
        # Carica le partite nel calendario
        self.calendar_table.load_matches_for_month(current_year, current_month, matches_in_month)
        
        # Imposta la data corrente se è nel mese visualizzato
        current_date = QDate.currentDate()
        if current_date.year() == current_year and current_date.month() == current_month:
            self.calendar_table.set_current_date(current_date)
    
    def on_month_changed(self):
        """Gestisce cambio mese"""
        self.refresh_display()
    
    def on_year_changed(self):
        """Gestisce cambio anno"""
        self.refresh_display()
    
    def go_to_prev_month(self):
        """Vai al mese precedente"""
        current_date = self.year_edit.date()
        new_date = current_date.addMonths(-1)
        self.month_combo.setCurrentIndex(new_date.month() - 1)
        self.year_edit.setDate(new_date)
    
    def go_to_next_month(self):
        """Vai al mese successivo"""
        current_date = self.year_edit.date()
        new_date = current_date.addMonths(1)
        self.month_combo.setCurrentIndex(new_date.month() - 1)
        self.year_edit.setDate(new_date)
    
    def go_to_today(self):
        """Vai a oggi"""
        today = QDate.currentDate()
        self.month_combo.setCurrentIndex(today.month() - 1)
        self.year_edit.setDate(today)
    
    def show_upcoming_matches(self):
        """Mostra prossime partite"""
        if not self.calendar:
            return
        
        # Ottieni prossime partite
        upcoming_matches = self.calendar.get_next_matches(limit=20)  # Le prossime 20 partite
        if upcoming_matches:
            # Mostra solo le partite per il mese della prima partita
            first_match_date = QDate(upcoming_matches[0].date.year, upcoming_matches[0].date.month, 1)
            self.month_combo.setCurrentIndex(first_match_date.month() - 1)
            self.year_edit.setDate(first_match_date)
    
    def show_all_matches(self):
        """Mostra tutte le partite"""
        if not self.calendar:
            return
        
        # Ottieni tutte le partite
        all_matches = self.calendar.matches
        if all_matches:
            # Visualizza il mese della prima partita
            first_match_date = QDate(all_matches[0].date.year, all_matches[0].date.month, 1)
            self.month_combo.setCurrentIndex(first_match_date.month() - 1)
            self.year_edit.setDate(first_match_date)