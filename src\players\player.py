"""
Modello Player per rappresentare i giocatori di calcio
"""
import datetime
from typing import Dict, Optional, List
from dataclasses import dataclass, field
from enum import Enum

from ..core.config import PLAYER_POSITIONS, PLAYER_ATTRIBUTES, PLAYER_AGE_RANGES
from ..core.utils import generate_unique_id, get_age_category, calculate_player_potential, clamp

class PlayerPosition(Enum):
    """Posizioni dei giocatori"""
    PORTIERE = "P"
    DIFENSORE_CENTRALE = "DC"
    DIFENSORE_SINISTRO = "DS"
    DIFENSORE_DESTRO = "DD"
    CENTROCAMPISTA_CENTRALE = "CC"
    CENTROCAMPISTA_SINISTRO = "CS"
    CENTROCAMPISTA_DESTRO = "CD"
    TREQUARTISTA = "T"
    ATTACCANTE_SINISTRO = "AS"
    ATTACCANTE_DESTRO = "AD"
    CENTRAVANTI = "C"

class FootPreference(Enum):
    """Piede preferito"""
    DESTRO = "Destro"
    SINISTRO = "Sinistro"
    AMBIDESTRO = "Ambidestro"

class ContractStatus(Enum):
    """Status contratto"""
    ATTIVO = "Attivo"
    IN_SCADENZA = "In Scadenza"
    SCADUTO = "Scaduto"
    PRESTITO = "Prestito"

class InjuryStatus(Enum):
    """Status infortunio"""
    SANO = "Sano"
    INFORTUNATO = "Infortunato"
    IN_RECUPERO = "In Recupero"

@dataclass
class PlayerAttributes:
    """Attributi tecnici del giocatore (scala 1-20)"""
    tecnica: int = 10
    velocita: int = 10
    forza: int = 10
    resistenza: int = 10
    tiro: int = 10
    passaggio: int = 10
    dribbling: int = 10
    difesa: int = 10
    portiere: int = 1  # Solo per portieri
    mentalita: int = 10

    def __post_init__(self):
        """Valida gli attributi dopo l'inizializzazione"""
        for attr_name in PLAYER_ATTRIBUTES:
            value = getattr(self, attr_name)
            setattr(self, attr_name, clamp(value, 1, 20))

    def get_overall_rating(self, position: PlayerPosition) -> int:
        """Calcola valutazione complessiva basata su posizione"""
        position_weights = self._get_position_weights(position)

        total_weighted = 0
        total_weight = 0

        for attr_name, weight in position_weights.items():
            if hasattr(self, attr_name):
                total_weighted += getattr(self, attr_name) * weight
                total_weight += weight

        if total_weight == 0:
            return 10

        return int(total_weighted / total_weight)

    def _get_position_weights(self, position: PlayerPosition) -> Dict[str, float]:
        """Restituisce pesi attributi per posizione"""
        weights = {
            PlayerPosition.PORTIERE: {
                "portiere": 3.0, "mentalita": 2.0, "velocita": 1.5,
                "tecnica": 1.0, "forza": 1.0, "resistenza": 1.0
            },
            PlayerPosition.DIFENSORE_CENTRALE: {
                "difesa": 3.0, "forza": 2.5, "mentalita": 2.0,
                "tecnica": 1.5, "resistenza": 1.5, "velocita": 1.0
            },
            PlayerPosition.DIFENSORE_SINISTRO: {
                "difesa": 2.5, "velocita": 2.5, "resistenza": 2.0,
                "passaggio": 1.5, "tecnica": 1.5, "dribbling": 1.0
            },
            PlayerPosition.DIFENSORE_DESTRO: {
                "difesa": 2.5, "velocita": 2.5, "resistenza": 2.0,
                "passaggio": 1.5, "tecnica": 1.5, "dribbling": 1.0
            },
            PlayerPosition.CENTROCAMPISTA_CENTRALE: {
                "passaggio": 3.0, "tecnica": 2.5, "mentalita": 2.0,
                "resistenza": 2.0, "difesa": 1.5, "dribbling": 1.5
            },
            PlayerPosition.CENTROCAMPISTA_SINISTRO: {
                "dribbling": 2.5, "velocita": 2.5, "passaggio": 2.0,
                "tecnica": 2.0, "resistenza": 1.5, "tiro": 1.5
            },
            PlayerPosition.CENTROCAMPISTA_DESTRO: {
                "dribbling": 2.5, "velocita": 2.5, "passaggio": 2.0,
                "tecnica": 2.0, "resistenza": 1.5, "tiro": 1.5
            },
            PlayerPosition.TREQUARTISTA: {
                "tecnica": 3.0, "dribbling": 2.5, "passaggio": 2.5,
                "tiro": 2.0, "velocita": 1.5, "mentalita": 2.0
            },
            PlayerPosition.ATTACCANTE_SINISTRO: {
                "tiro": 2.5, "velocita": 2.5, "dribbling": 2.5,
                "tecnica": 2.0, "mentalita": 1.5, "forza": 1.0
            },
            PlayerPosition.ATTACCANTE_DESTRO: {
                "tiro": 2.5, "velocita": 2.5, "dribbling": 2.5,
                "tecnica": 2.0, "mentalita": 1.5, "forza": 1.0
            },
            PlayerPosition.CENTRAVANTI: {
                "tiro": 3.0, "forza": 2.5, "mentalita": 2.0,
                "tecnica": 2.0, "velocita": 1.5, "dribbling": 1.0
            }
        }

        return weights.get(position, {})

@dataclass
class PlayerStats:
    """Statistiche stagionali giocatore"""
    presenze: int = 0
    minuti_giocati: int = 0
    gol: int = 0
    assist: int = 0
    cartellini_gialli: int = 0
    cartellini_rossi: int = 0
    voto_medio: float = 6.0

    def add_match_stats(self, minutes: int, goals: int = 0, assists: int = 0,
                       yellow_cards: int = 0, red_cards: int = 0, rating: float = 6.0):
        """Aggiunge statistiche di una partita"""
        if minutes > 0:
            self.presenze += 1
            self.minuti_giocati += minutes
            self.gol += goals
            self.assist += assists
            self.cartellini_gialli += yellow_cards
            self.cartellini_rossi += red_cards

            # Calcola nuovo voto medio
            total_ratings = (self.voto_medio * (self.presenze - 1)) + rating
            self.voto_medio = total_ratings / self.presenze

@dataclass
class PlayerContract:
    """Contratto del giocatore"""
    stipendio_annuale: int = 50000  # in euro
    data_scadenza: datetime.date = field(default_factory=lambda: datetime.date.today())
    clausola_rescissoria: Optional[int] = None
    status: ContractStatus = ContractStatus.ATTIVO

    def is_expiring_soon(self, months: int = 6) -> bool:
        """Verifica se il contratto scade presto"""
        today = datetime.date.today()
        expiry_threshold = today + datetime.timedelta(days=months * 30)
        return self.data_scadenza <= expiry_threshold

    def is_expired(self) -> bool:
        """Verifica se il contratto è scaduto"""
        return self.data_scadenza <= datetime.date.today()

class Player:
    """Classe principale per rappresentare un giocatore"""

    def __init__(self, first_name: str, last_name: str, nationality: str,
                 birth_date: datetime.date, position: PlayerPosition):

        # Info base
        self.id = generate_unique_id()
        self.first_name = first_name
        self.last_name = last_name
        self.nationality = nationality
        self.birth_date = birth_date

        # Info calcistiche
        self.position = position
        self.secondary_positions: List[PlayerPosition] = []
        self.foot_preference = FootPreference.DESTRO
        self.shirt_number: Optional[int] = None

        # Attributi e sviluppo
        self.attributes = PlayerAttributes()
        self.potential = calculate_player_potential(
            self.attributes.get_overall_rating(position), self.age
        )

        # Status
        self.injury_status = InjuryStatus.SANO
        self.injury_days_left = 0
        self.morale = 75  # scala 1-100
        self.fitness = 100  # scala 1-100

        # Statistiche e contratto
        self.current_season_stats = PlayerStats()
        self.career_stats = PlayerStats()
        self.contract = PlayerContract()

        # Club
        self.current_club: Optional[str] = None
        self.is_on_loan = False
        self.loan_club: Optional[str] = None

        # Valore di mercato (calcolato)
        self._market_value = self._calculate_market_value()

    @property
    def full_name(self) -> str:
        """Nome completo del giocatore"""
        return f"{self.first_name} {self.last_name}"

    @property
    def age(self) -> int:
        """Età del giocatore"""
        today = datetime.date.today()
        return today.year - self.birth_date.year - (
            (today.month, today.day) < (self.birth_date.month, self.birth_date.day)
        )

    @property
    def age_category(self) -> str:
        """Categoria d'età"""
        return get_age_category(self.age)

    @property
    def overall_rating(self) -> int:
        """Valutazione complessiva"""
        return self.attributes.get_overall_rating(self.position)

    @property
    def market_value(self) -> int:
        """Valore di mercato attuale"""
        return self._market_value

    @property
    def is_injured(self) -> bool:
        """Verifica se il giocatore è infortunato"""
        return self.injury_status in [InjuryStatus.INFORTUNATO, InjuryStatus.IN_RECUPERO]

    @property
    def is_available(self) -> bool:
        """Verifica se il giocatore è disponibile per giocare"""
        return not self.is_injured and self.fitness >= 50

    def _calculate_market_value(self) -> int:
        """Calcola valore di mercato basato su vari fattori"""
        base_value = 50000  # valore base

        # Fattore overall rating
        rating_multiplier = (self.overall_rating / 10) ** 2

        # Fattore età
        age = self.age
        if age <= 21:
            age_multiplier = 1.5  # Giovani promettenti
        elif age <= 25:
            age_multiplier = 2.0  # Picco valore
        elif age <= 29:
            age_multiplier = 1.5  # Ancora buon valore
        elif age <= 32:
            age_multiplier = 1.0  # Valore normale
        else:
            age_multiplier = 0.5  # Veterani

        # Fattore posizione (alcuni ruoli valgono di più)
        position_multipliers = {
            PlayerPosition.PORTIERE: 0.8,
            PlayerPosition.DIFENSORE_CENTRALE: 0.9,
            PlayerPosition.CENTROCAMPISTA_CENTRALE: 1.2,
            PlayerPosition.TREQUARTISTA: 1.3,
            PlayerPosition.CENTRAVANTI: 1.4
        }
        position_multiplier = position_multipliers.get(self.position, 1.0)

        # Fattore potenziale per giovani
        potential_bonus = 1.0
        if age <= 23 and self.potential > self.overall_rating:
            potential_bonus = 1.0 + ((self.potential - self.overall_rating) / 20)

        total_value = int(base_value * rating_multiplier * age_multiplier *
                         position_multiplier * potential_bonus)

        return max(total_value, 10000)  # Valore minimo

    def improve_attributes(self, training_quality: float = 1.0):
        """Migliora attributi basato su allenamento"""
        if self.age > 30:
            return  # Giocatori anziani non migliorano

        improvement_chance = 0.1 * training_quality

        if self.age <= 21:
            improvement_chance *= 2.0  # Giovani migliorano di più
        elif self.age <= 25:
            improvement_chance *= 1.5

        # Migliora attributi casuali
        import random

        for attr_name in PLAYER_ATTRIBUTES:
            if random.random() < improvement_chance:
                current_value = getattr(self.attributes, attr_name)
                if current_value < self.potential:
                    new_value = min(current_value + 1, 20)
                    setattr(self.attributes, attr_name, new_value)

    def apply_injury(self, days: int, injury_type: str = "Infortunio"):
        """Applica infortunio al giocatore"""
        self.injury_status = InjuryStatus.INFORTUNATO
        self.injury_days_left = days
        self.fitness = max(self.fitness - 20, 30)

    def heal_injury(self):
        """Guarisce completamente dall'infortunio"""
        self.injury_status = InjuryStatus.SANO
        self.injury_days_left = 0

    def update_daily(self):
        """Aggiornamento giornaliero del giocatore"""
        # Recupero infortunio
        if self.is_injured and self.injury_days_left > 0:
            self.injury_days_left -= 1
            if self.injury_days_left == 0:
                self.injury_status = InjuryStatus.IN_RECUPERO

        # Recupero fitness se in recupero
        if self.injury_status == InjuryStatus.IN_RECUPERO:
            self.fitness = min(self.fitness + 5, 100)
            if self.fitness >= 90:
                self.heal_injury()

        # Ricalcola valore di mercato periodicamente
        self._market_value = self._calculate_market_value()

    def to_dict(self) -> Dict:
        """Converte giocatore in dizionario per serializzazione"""
        return {
            "id": self.id,
            "first_name": self.first_name,
            "last_name": self.last_name,
            "nationality": self.nationality,
            "birth_date": self.birth_date.isoformat(),
            "position": self.position.value,
            "attributes": {
                attr: getattr(self.attributes, attr)
                for attr in PLAYER_ATTRIBUTES
            },
            "overall_rating": self.overall_rating,
            "market_value": self.market_value,
            "age": self.age
        }

    def __str__(self) -> str:
        """Rappresentazione stringa del giocatore"""
        return f"{self.full_name} ({self.age}) - {self.position.value} - Overall: {self.overall_rating}"