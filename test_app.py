"""
Script di test per verificare il funzionamento dell'applicazione
"""
import sys
from pathlib import Path

# Aggiunge src al path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from src.core.config import *
from src.data.data_loader import data_loader
from src.data.name_generator import name_generator

def test_data_loading():
    """Test caricamento dati"""
    print("=== TEST CARICAMENTO DATI ===")

    # Test caricamento
    success = data_loader.load_all_data()
    print(f"Caricamento dati: {'OK' if success else 'ERRORE'}")

    # Test validazione
    valid = data_loader.validate_data_integrity()
    print(f"Validazione dati: {'OK' if valid else 'ERRORE'}")

    # Statistiche
    stats = data_loader.get_statistics()
    print(f"Nazioni con nomi: {stats.get('nazioni_nomi', 0)}")
    print(f"Squadre italiane totali: {stats.get('squadre_italiane', 0)}")
    print(f"Serie A: {stats.get('squadre_serie_a', 0)} squadre")
    print(f"Serie B: {stats.get('squadre_serie_b', 0)} squadre")
    print(f"Serie C: {stats.get('squadre_serie_c', 0)} squadre")

def test_name_generator():
    """Test generatore nomi"""
    print("\n=== TEST GENERATORE NOMI ===")

    # Assicurati che i dati siano caricati
    if not data_loader.is_loaded():
        data_loader.load_all_data()

    # Test validazione
    valid = name_generator.validate_names_availability()
    print(f"Generatore nomi funzionante: {'OK' if valid else 'ERRORE'}")

    # Test generazione nomi
    for i in range(5):
        first_name, last_name, nation = name_generator.generate_random_name()
        print(f"{i+1}. {first_name} {last_name} ({nation})")

    # Test nome italiano specifico
    italian_name = name_generator.generate_name_from_nation("Italia")
    if italian_name:
        print(f"Nome italiano: {italian_name[0]} {italian_name[1]}")

def test_team_data():
    """Test dati squadre"""
    print("\n=== TEST DATI SQUADRE ===")

    # Serie A
    serie_a_teams = data_loader.get_serie_a_teams()
    print(f"Squadre Serie A: {len(serie_a_teams)}")
    if serie_a_teams:
        team = serie_a_teams[0]
        print(f"Prima squadra: {team.get('nome', 'N/A')} - {team.get('citta', 'N/A')}")

    # Test ricerca squadra
    napoli = data_loader.get_team_by_name("Napoli")
    if napoli:
        print(f"Napoli trovato - Reputazione: {napoli.get('reputazione', 'N/A')}/20")

    # Serie C
    serie_c_teams = data_loader.get_serie_c_teams()
    print(f"Squadre Serie C: {len(serie_c_teams)} (dovrebbero essere ~60)")

if __name__ == "__main__":
    try:
        ensure_directories()
        test_data_loading()
        test_name_generator()
        test_team_data()
        print("\n=== TUTTI I TEST COMPLETATI ===")

    except Exception as e:
        print(f"Errore durante i test: {e}")
        sys.exit(1)