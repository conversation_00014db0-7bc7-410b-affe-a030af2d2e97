"""
GameEngine - Cuore pulsante di Football Manager <PERSON><PERSON>
Gestisce l'avanzamento del tempo e tutti gli eventi del gioco con elaborazione giornaliera
"""

import datetime
from typing import List, Dict, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
from PyQt6.QtCore import QObject, pyqtSignal, QThread, QMutex, QMutexLocker

from ..core.utils import logger
from ..competitions.season import Season
from ..competitions.competition import CompetitionManager
from ..competitions.calendar import MatchCalendar, Match


class StopPointType(Enum):
    """Tipi di punti di arresto"""
    PLAYER_MATCH = "player_match"           # Partita della squadra del giocatore
    IMPORTANT_MATCH = "important_match"     # Partita importante nel campionato
    TRANSFER_WINDOW = "transfer_window"     # Inizio/fine finestra mercato
    IMPORTANT_NEWS = "important_news"       # Notizia importante
    USER_REQUEST = "user_request"           # Fine simulazione richiesta dall'utente
    SEASON_EVENT = "season_event"           # Eventi stagionali (inizio/fine campionato)


@dataclass
class StopPoint:
    """Punto di arresto nella simulazione"""
    date: datetime.date
    stop_type: StopPointType
    description: str
    priority: int = 1  # 1=alta, 2=media, 3=bassa
    data: Optional[Dict] = None  # Dati aggiuntivi per l'evento
    
    def __lt__(self, other):
        """Ordinamento per data e priorità"""
        if self.date != other.date:
            return self.date < other.date
        return self.priority < other.priority


class GameEngine(QObject):
    """
    Motore centrale del gioco - Gestisce tempo ed eventi
    
    Filosofia: Elaborazione giornaliera sequenziale con stop points
    """
    
    # Segnali per comunicazione con UI
    date_changed = pyqtSignal(datetime.date)  # Data cambiata
    simulation_stopped = pyqtSignal(str, dict)  # Simulazione fermata (motivo, dati)
    match_simulated = pyqtSignal(dict)  # Partita simulata (risultato)
    matches_simulated = pyqtSignal(list)  # Più partite simulate (risultati)
    season_event = pyqtSignal(str, dict)  # Evento stagionale
    progress_update = pyqtSignal(int, int)  # Progresso simulazione (current, total)
    
    def __init__(self, season: Season, competition_manager: CompetitionManager, 
                 calendar: MatchCalendar, player_team: Optional[str] = None):
        super().__init__()
        
        # Componenti principali
        self.season = season
        self.competition_manager = competition_manager
        self.calendar = calendar
        self.player_team = player_team  # Squadra del giocatore
        
        # Stato simulazione
        self.is_running = False
        self.stop_points: List[StopPoint] = []
        self.current_stop_point: Optional[StopPoint] = None
        
        # Thread safety
        self.mutex = QMutex()
        
        # Inizializza stop points
        self._rebuild_stop_points()
        
        logger.info("GameEngine inizializzato")
    
    def set_player_team(self, team_name: str):
        """Imposta la squadra del giocatore"""
        self.player_team = team_name
        self._rebuild_stop_points()  # Ricostruisci stop points con nuova squadra
        logger.info(f"Squadra giocatore impostata: {team_name}")
    
    def _rebuild_stop_points(self):
        """Ricostruisce la coda dei punti di arresto"""
        with QMutexLocker(self.mutex):
            self.stop_points.clear()
            current_date = self.season.current_date
            
            # 1. Partite della squadra del giocatore (priorità massima)
            if self.player_team:
                player_matches = self.calendar.get_matches_for_team(self.player_team)
                for match in player_matches:
                    if match.date >= current_date and not match.played:
                        self.stop_points.append(StopPoint(
                            date=match.date,
                            stop_type=StopPointType.PLAYER_MATCH,
                            description=f"Partita: {match.home_team} vs {match.away_team}",
                            priority=1,
                            data={"match": match}
                        ))
            
            # 2. Partite importanti (derby, big match, etc.)
            all_matches = self.calendar.get_next_matches(limit=100)
            for match in all_matches:
                if match.date >= current_date and not match.played:
                    if self._is_important_match(match):
                        self.stop_points.append(StopPoint(
                            date=match.date,
                            stop_type=StopPointType.IMPORTANT_MATCH,
                            description=f"Big Match: {match.home_team} vs {match.away_team}",
                            priority=2,
                            data={"match": match}
                        ))
            
            # 3. Eventi stagionali (inizio/fine campionato, finestre mercato)
            self._add_season_events()
            
            # Ordina per data e priorità
            self.stop_points.sort()
            
            logger.info(f"Ricostruiti {len(self.stop_points)} stop points")
    
    def _add_season_events(self):
        """Aggiunge eventi stagionali ai stop points"""
        current_date = self.season.current_date
        
        # Finestre di mercato
        if self.season.dates.summer_window_start >= current_date:
            self.stop_points.append(StopPoint(
                date=self.season.dates.summer_window_start,
                stop_type=StopPointType.TRANSFER_WINDOW,
                description="Apertura mercato estivo",
                priority=2,
                data={"window_type": "summer_start"}
            ))
        
        if self.season.dates.summer_window_end >= current_date:
            self.stop_points.append(StopPoint(
                date=self.season.dates.summer_window_end,
                stop_type=StopPointType.TRANSFER_WINDOW,
                description="Chiusura mercato estivo",
                priority=2,
                data={"window_type": "summer_end"}
            ))
        
        if self.season.dates.winter_window_start >= current_date:
            self.stop_points.append(StopPoint(
                date=self.season.dates.winter_window_start,
                stop_type=StopPointType.TRANSFER_WINDOW,
                description="Apertura mercato invernale",
                priority=2,
                data={"window_type": "winter_start"}
            ))
        
        if self.season.dates.winter_window_end >= current_date:
            self.stop_points.append(StopPoint(
                date=self.season.dates.winter_window_end,
                stop_type=StopPointType.TRANSFER_WINDOW,
                description="Chiusura mercato invernale",
                priority=2,
                data={"window_type": "winter_end"}
            ))
        
        # Inizio e fine campionato
        if self.season.dates.league_start >= current_date:
            self.stop_points.append(StopPoint(
                date=self.season.dates.league_start,
                stop_type=StopPointType.SEASON_EVENT,
                description="Inizio campionato",
                priority=1,
                data={"event_type": "league_start"}
            ))
        
        if self.season.dates.league_end >= current_date:
            self.stop_points.append(StopPoint(
                date=self.season.dates.league_end,
                stop_type=StopPointType.SEASON_EVENT,
                description="Fine campionato",
                priority=1,
                data={"event_type": "league_end"}
            ))
    
    def _is_important_match(self, match: Match) -> bool:
        """Determina se una partita è importante"""
        # Derby (stessa città/regione)
        if self._is_derby(match.home_team, match.away_team):
            return True
        
        # Big match (squadre top)
        top_teams = ["Juventus", "Inter Milan", "AC Milan", "Napoli", "AS Roma", "Lazio", "Atalanta"]
        if match.home_team in top_teams and match.away_team in top_teams:
            return True
        
        # Partite UEFA
        if "UEFA" in match.competition:
            return True
        
        # Finali di coppa
        if "Finale" in match.competition or "Final" in match.competition:
            return True
        
        return False
    
    def _is_derby(self, team1: str, team2: str) -> bool:
        """Verifica se è un derby"""
        # Derby di Milano
        milan_teams = ["AC Milan", "Inter Milan"]
        if team1 in milan_teams and team2 in milan_teams:
            return True
        
        # Derby di Roma
        rome_teams = ["AS Roma", "Lazio"]
        if team1 in rome_teams and team2 in rome_teams:
            return True
        
        # Derby di Torino
        turin_teams = ["Juventus", "Torino"]
        if team1 in turin_teams and team2 in turin_teams:
            return True
        
        # Altri derby possono essere aggiunti qui
        return False

    def get_next_stop_point(self) -> Optional[StopPoint]:
        """Restituisce il prossimo punto di arresto"""
        with QMutexLocker(self.mutex):
            current_date = self.season.current_date

            # Filtra stop points futuri
            future_stops = [sp for sp in self.stop_points if sp.date >= current_date]

            if not future_stops:
                return None

            return future_stops[0]

    def run_simulation_until_next_stop(self):
        """
        METODO PRINCIPALE: Esegue simulazione fino al prossimo stop point
        Questo è il cuore del sistema - elaborazione giornaliera sequenziale
        """
        if self.is_running:
            logger.warning("Simulazione già in corso")
            return

        next_stop = self.get_next_stop_point()
        if not next_stop:
            logger.info("Nessun stop point trovato - stagione completata?")
            self.simulation_stopped.emit("season_completed", {})
            return

        self.is_running = True
        self.current_stop_point = next_stop

        logger.info(f"Avvio simulazione fino a: {next_stop.date} ({next_stop.description})")

        # Calcola giorni da simulare
        current_date = self.season.current_date
        days_to_simulate = (next_stop.date - current_date).days

        if days_to_simulate < 0:
            logger.error("Stop point nel passato!")
            self.is_running = False
            return

        # CRITICO: Se stop point è oggi, processa il giorno corrente PRIMA di fermarsi
        if days_to_simulate == 0 and next_stop.date == current_date:
            logger.info(f"Stop point è oggi ({current_date}), processando il giorno corrente")

            # IMPORTANTE: Processa il giorno corrente per simulare le partite di oggi
            try:
                self._process_day(current_date)
                self.progress_update.emit(1, 1)
            except Exception as e:
                logger.error(f"Errore durante elaborazione giorno corrente: {e}")

            # Ora rimuovi lo stop point e fermati
            with QMutexLocker(self.mutex):
                if next_stop in self.stop_points:
                    self.stop_points.remove(next_stop)
                    logger.info(f"Stop point rimosso dopo elaborazione: {next_stop.description}")

            # Emetti segnale per questo stop point
            self.simulation_stopped.emit(next_stop.stop_type.value, next_stop.data or {})
            self.is_running = False
            return

        # Elaborazione giornaliera
        try:
            for day_offset in range(days_to_simulate + 1):
                target_date = current_date + datetime.timedelta(days=day_offset)

                # Elabora questo giorno
                self._process_day(target_date)

                # Emetti progresso
                self.progress_update.emit(day_offset + 1, days_to_simulate + 1)

                # Se abbiamo raggiunto lo stop point, fermati
                if target_date >= next_stop.date:
                    break

            # Simulazione completata - RIMUOVI LO STOP POINT ELABORATO
            self.is_running = False

            # CRITICO: Rimuovi stop point elaborato per evitare loop infinito
            with QMutexLocker(self.mutex):
                if next_stop in self.stop_points:
                    self.stop_points.remove(next_stop)
                    logger.info(f"Stop point rimosso: {next_stop.description}")

            self.simulation_stopped.emit(next_stop.stop_type.value, next_stop.data or {})

            logger.info(f"Simulazione completata - fermata per: {next_stop.description}")

        except Exception as e:
            logger.error(f"Errore durante simulazione: {e}")
            self.is_running = False
            self.simulation_stopped.emit("error", {"error": str(e)})

    def _process_day(self, target_date: datetime.date):
        """
        CUORE DELLA SIMULAZIONE: Elabora un singolo giorno

        Ordine delle operazioni:
        1. Avanza la data
        2. Simula le partite del giorno
        3. Aggiorna i giocatori
        4. Genera notizie
        5. Emette segnali
        """
        logger.debug(f"Elaborando giorno: {target_date}")

        # 1. Avanza la data nella stagione
        if target_date > self.season.current_date:
            days_to_advance = (target_date - self.season.current_date).days
            season_events = self.season.advance_date(days_to_advance)

            # Emetti eventi stagionali
            for event in season_events:
                self.season_event.emit("season_phase_change", {"event": event})

        # 2. Simula partite del giorno
        matches_today = self.calendar.get_matches_for_date(target_date)
        if matches_today:
            self._simulate_matches_for_day(matches_today, target_date)

        # 3. Aggiornamenti giornalieri giocatori
        self._update_players_daily()

        # 4. Genera notizie (placeholder per funzionalità futura)
        self._generate_daily_news(target_date)

        # 5. Emetti segnale cambio data
        self.date_changed.emit(target_date)

    def _simulate_matches_for_day(self, matches: List[Match], date: datetime.date):
        """Simula tutte le partite di un giorno"""
        if not matches:
            return

        # Ordina partite per orario
        matches.sort(key=lambda m: m.time if m.time else "15:00")

        results = []

        for match in matches:
            if match.played:
                continue

            # Trova la competizione per questa partita
            competition = self.competition_manager.get_competition(match.competition)
            if competition and hasattr(competition, 'simulate_match'):
                try:
                    result = competition.simulate_match(match)
                    if "error" not in result:
                        results.append(result)

                        # Emetti segnale per singola partita
                        self.match_simulated.emit(result)

                        logger.info(f"Simulata: {match.home_team} {result['home_score']}-{result['away_score']} {match.away_team}")

                except Exception as e:
                    logger.error(f"Errore simulazione partita {match.home_team} vs {match.away_team}: {e}")

        # Emetti segnale per tutte le partite del giorno
        if results:
            self.matches_simulated.emit(results)
            logger.info(f"Simulate {len(results)} partite il {date}")

    def _update_players_daily(self):
        """Aggiornamenti giornalieri dei giocatori"""
        # Placeholder per aggiornamenti giocatori:
        # - Recupero forma fisica
        # - Recupero da infortuni
        # - Calo per età
        # - Cambiamenti di morale
        # - Training effects
        pass

    def _generate_daily_news(self, target_date: datetime.date):
        """Genera notizie giornaliere"""
        # Placeholder per sistema notizie:
        # - Voci di mercato
        # - Commenti pre/post partita
        # - Infortuni
        # - Dichiarazioni allenatori
        pass

    def stop_simulation(self):
        """Ferma la simulazione in corso"""
        if self.is_running:
            self.is_running = False
            logger.info("Simulazione fermata dall'utente")
            self.simulation_stopped.emit("user_stopped", {})

    def skip_to_date(self, target_date: datetime.date):
        """Salta direttamente a una data specifica"""
        if target_date <= self.season.current_date:
            logger.warning("Data target nel passato o uguale a quella attuale")
            return

        # Crea stop point temporaneo
        temp_stop = StopPoint(
            date=target_date,
            stop_type=StopPointType.USER_REQUEST,
            description=f"Salta a {target_date}",
            priority=1
        )

        # Salva stop point corrente
        original_stop = self.current_stop_point
        self.current_stop_point = temp_stop

        # Esegui simulazione
        self.run_simulation_until_next_stop()

        # Ripristina stop point originale
        self.current_stop_point = original_stop

    def get_simulation_status(self) -> Dict:
        """Restituisce stato attuale della simulazione"""
        next_stop = self.get_next_stop_point()

        return {
            "is_running": self.is_running,
            "current_date": self.season.current_date,
            "current_phase": self.season.current_phase.value,
            "next_stop_point": {
                "date": next_stop.date,
                "type": next_stop.stop_type.value,
                "description": next_stop.description,
                "days_until": (next_stop.date - self.season.current_date).days
            } if next_stop else None,
            "total_stop_points": len(self.stop_points),
            "player_team": self.player_team
        }

    def get_upcoming_events(self, limit: int = 10) -> List[Dict]:
        """Restituisce prossimi eventi"""
        with QMutexLocker(self.mutex):
            current_date = self.season.current_date
            upcoming = [sp for sp in self.stop_points if sp.date >= current_date][:limit]

            return [{
                "date": sp.date,
                "type": sp.stop_type.value,
                "description": sp.description,
                "priority": sp.priority,
                "days_until": (sp.date - current_date).days
            } for sp in upcoming]

    def refresh_stop_points(self):
        """Ricostruisce i stop points (da chiamare dopo cambiamenti significativi)"""
        self._rebuild_stop_points()
        logger.info("Stop points aggiornati")

    def add_custom_stop_point(self, date: datetime.date, description: str,
                             priority: int = 2, data: Optional[Dict] = None):
        """Aggiunge un stop point personalizzato"""
        if date <= self.season.current_date:
            logger.warning("Impossibile aggiungere stop point nel passato")
            return

        custom_stop = StopPoint(
            date=date,
            stop_type=StopPointType.USER_REQUEST,
            description=description,
            priority=priority,
            data=data
        )

        with QMutexLocker(self.mutex):
            self.stop_points.append(custom_stop)
            self.stop_points.sort()

        logger.info(f"Aggiunto stop point personalizzato: {description} ({date})")

    def get_matches_until_next_stop(self) -> List[Match]:
        """Restituisce partite fino al prossimo stop point"""
        next_stop = self.get_next_stop_point()
        if not next_stop:
            return []

        return self.calendar.get_matches_in_date_range(
            self.season.current_date,
            next_stop.date
        )

    def __str__(self) -> str:
        """Rappresentazione stringa del GameEngine"""
        status = self.get_simulation_status()
        return (f"GameEngine(running={status['is_running']}, "
                f"date={status['current_date']}, "
                f"phase={status['current_phase']}, "
                f"team={status['player_team']})")


class GameEngineThread(QThread):
    """Thread separato per eseguire GameEngine senza bloccare UI"""

    def __init__(self, game_engine: GameEngine):
        super().__init__()
        self.game_engine = game_engine
        self.should_run = False

    def run(self):
        """Esegue simulazione su thread separato"""
        if self.should_run:
            self.game_engine.run_simulation_until_next_stop()

    def start_simulation(self):
        """Avvia simulazione su thread"""
        self.should_run = True
        self.start()

    def stop_simulation(self):
        """Ferma simulazione"""
        self.should_run = False
        self.game_engine.stop_simulation()
        self.wait()  # Aspetta che il thread finisca
