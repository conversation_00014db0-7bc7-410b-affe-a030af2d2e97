#!/usr/bin/env python3
"""
Test per il nuovo GameEngine
Verifica il funzionamento del sistema di elaborazione giornaliera
"""

import sys
import datetime
sys.path.append('.')

from src.core.game_engine import GameEngine, StopPoint, StopPointType
from src.competitions.season import Season
from src.competitions.competition import setup_italian_competitions
from src.competitions.calendar import MatchCalendar, generate_full_season_calendar
from src.data.data_loader import data_loader


def test_game_engine_creation():
    """Test creazione GameEngine"""
    print("🎯 TEST CREAZIONE GAMEENGINE")
    print("=" * 50)
    
    # Carica dati
    print("Caricamento dati...")
    data_loader.load_all_data()
    
    # Crea componenti
    season = Season(2025)

    # Setup competizioni italiane
    teams_by_level = {
        1: [team["nome"] for team in data_loader.get_serie_a_teams()],
        2: [team["nome"] for team in data_loader.get_serie_b_teams()]
    }
    serie_c_groups = data_loader.get_serie_c_groups()
    competition_manager = setup_italian_competitions(season, teams_by_level, serie_c_groups)
    
    # Genera calendario
    calendar = generate_full_season_calendar(season, competition_manager)
    
    # Crea GameEngine
    game_engine = GameEngine(
        season=season,
        competition_manager=competition_manager,
        calendar=calendar,
        player_team="Juventus"  # Squadra del giocatore
    )
    
    print(f"✅ GameEngine creato: {game_engine}")
    
    # Mostra stato
    status = game_engine.get_simulation_status()
    print(f"📅 Data corrente: {status['current_date']}")
    print(f"🏆 Fase: {status['current_phase']}")
    print(f"⚽ Squadra: {status['player_team']}")
    print(f"📊 Stop points: {status['total_stop_points']}")
    
    if status['next_stop_point']:
        next_stop = status['next_stop_point']
        print(f"🎯 Prossimo evento: {next_stop['description']}")
        print(f"📆 Data: {next_stop['date']}")
        print(f"⏰ Tra {next_stop['days_until']} giorni")
    
    return game_engine


def test_stop_points(game_engine: GameEngine):
    """Test sistema stop points"""
    print("\n🎯 TEST STOP POINTS")
    print("=" * 50)
    
    # Mostra prossimi eventi
    upcoming = game_engine.get_upcoming_events(limit=10)
    
    print("📋 Prossimi 10 eventi:")
    for i, event in enumerate(upcoming, 1):
        print(f"{i:2d}. {event['date']} - {event['description']} "
              f"(tra {event['days_until']} giorni) [{event['type']}]")
    
    # Test aggiunta stop point personalizzato
    custom_date = game_engine.season.current_date + datetime.timedelta(days=5)
    game_engine.add_custom_stop_point(
        date=custom_date,
        description="Test evento personalizzato",
        priority=1
    )
    
    print(f"\n✅ Aggiunto stop point personalizzato per {custom_date}")
    
    # Mostra eventi aggiornati
    updated_upcoming = game_engine.get_upcoming_events(limit=5)
    print("\n📋 Eventi aggiornati (primi 5):")
    for i, event in enumerate(updated_upcoming, 1):
        print(f"{i:2d}. {event['date']} - {event['description']} "
              f"(tra {event['days_until']} giorni)")


def test_simulation_dry_run(game_engine: GameEngine):
    """Test simulazione senza esecuzione reale"""
    print("\n🎯 TEST SIMULAZIONE (DRY RUN)")
    print("=" * 50)
    
    # Stato iniziale
    initial_date = game_engine.season.current_date
    next_stop = game_engine.get_next_stop_point()
    
    if not next_stop:
        print("❌ Nessun stop point trovato")
        return
    
    print(f"📅 Data iniziale: {initial_date}")
    print(f"🎯 Prossimo stop: {next_stop.date} ({next_stop.description})")
    
    days_to_simulate = (next_stop.date - initial_date).days
    print(f"⏰ Giorni da simulare: {days_to_simulate}")
    
    # Mostra partite nel periodo
    matches = game_engine.get_matches_until_next_stop()
    print(f"⚽ Partite nel periodo: {len(matches)}")
    
    for match in matches[:5]:  # Prime 5
        print(f"   {match.date} - {match.home_team} vs {match.away_team} ({match.competition})")
    
    if len(matches) > 5:
        print(f"   ... e altre {len(matches) - 5} partite")


def test_simulation_execution(game_engine: GameEngine):
    """Test esecuzione simulazione reale"""
    print("\n🎯 TEST ESECUZIONE SIMULAZIONE")
    print("=" * 50)
    
    # Collega segnali per test
    def on_date_changed(new_date):
        print(f"📅 Data cambiata: {new_date}")
    
    def on_match_simulated(result):
        print(f"⚽ Partita: {result['home_team']} {result['home_score']}-{result['away_score']} {result['away_team']}")
    
    def on_simulation_stopped(reason, data):
        print(f"🛑 Simulazione fermata: {reason}")
        if data:
            print(f"   Dati: {data}")
    
    def on_progress_update(current, total):
        if total > 0:
            progress = int((current / total) * 100)
            print(f"📊 Progresso: {progress}% ({current}/{total})")
    
    # Collega segnali
    game_engine.date_changed.connect(on_date_changed)
    game_engine.match_simulated.connect(on_match_simulated)
    game_engine.simulation_stopped.connect(on_simulation_stopped)
    game_engine.progress_update.connect(on_progress_update)
    
    # Stato prima della simulazione
    initial_status = game_engine.get_simulation_status()
    print(f"📅 Data iniziale: {initial_status['current_date']}")
    
    # Esegui simulazione
    print("\n🚀 Avvio simulazione...")
    game_engine.run_simulation_until_next_stop()
    
    # Stato dopo la simulazione
    final_status = game_engine.get_simulation_status()
    print(f"\n📅 Data finale: {final_status['current_date']}")
    print(f"🏆 Fase finale: {final_status['current_phase']}")
    
    # Calcola giorni simulati
    days_simulated = (final_status['current_date'] - initial_status['current_date']).days
    print(f"⏰ Giorni simulati: {days_simulated}")


def main():
    """Test principale"""
    print("🎮 TEST GAMEENGINE - FOOTBALL MANAGER ITALIANO")
    print("=" * 60)
    
    try:
        # Test 1: Creazione
        game_engine = test_game_engine_creation()
        
        # Test 2: Stop Points
        test_stop_points(game_engine)
        
        # Test 3: Simulazione Dry Run
        test_simulation_dry_run(game_engine)
        
        # Test 4: Esecuzione Simulazione
        test_simulation_execution(game_engine)
        
        print("\n🎉 TUTTI I TEST COMPLETATI CON SUCCESSO!")
        
    except Exception as e:
        print(f"\n❌ ERRORE DURANTE I TEST: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
