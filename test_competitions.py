"""
Test del sistema competizioni e stagioni
"""
import sys
from pathlib import Path

src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from src.core.config import ensure_directories
from src.data.data_loader import data_loader
from src.competitions.season import Season
from src.competitions.competition import setup_italian_competitions
from src.competitions.calendar import generate_italian_league_calendar
from src.competitions.match_engine import match_simulator
from src.players.player_generator import player_generator

def test_season_creation():
    """Test creazione stagione"""
    print("=== TEST CREAZIONE STAGIONE ===")

    season = Season(2025)
    print(f"Stagione creata: {season}")
    print(f"Data corrente: {season.get_formatted_date()}")
    print(f"Fase attuale: {season.current_phase.value}")
    print(f"Mercato: {season.get_transfer_window().value}")
    print(f"Giorni fino inizio campionato: {season.days_until_season_start}")

    # Test avanzamento tempo
    print("\n--- Avanzamento di 30 giorni ---")
    events = season.advance_date(30)
    if events:
        print("Eventi:", events)

    print(f"Nuova data: {season.get_formatted_date()}")
    print(f"Nuova fase: {season.current_phase.value}")

def test_competitions_setup():
    """Test setup competizioni"""
    print("\n=== TEST SETUP COMPETIZIONI ===")

    season = Season(2025)

    # Prepara squadre per livello
    teams_by_level = {
        1: [team["nome"] for team in data_loader.get_serie_a_teams()],
        2: [team["nome"] for team in data_loader.get_serie_b_teams()]
    }

    # Ottieni i gironi di Serie C separatamente
    serie_c_groups = data_loader.get_serie_c_groups()

    print(f"Squadre Serie A: {len(teams_by_level[1])}")
    print(f"Squadre Serie B: {len(teams_by_level[2])}")
    print("Serie C:")
    for group_name, teams in serie_c_groups.items():
        print(f"  {group_name}: {len(teams)} squadre")

    # Crea competizioni
    competition_manager = setup_italian_competitions(season, teams_by_level, serie_c_groups)
    competitions = competition_manager.get_all_competitions()

    print(f"\nCompetizioni create: {len(competitions)}")
    for comp in competitions:
        print(f"  {comp.name}: {len(comp.teams)} squadre")

    return season, competition_manager, teams_by_level

def test_calendar_generation():
    """Test generazione calendario"""
    print("\n=== TEST GENERAZIONE CALENDARIO ===")

    season, competition_manager, teams_by_level = test_competitions_setup()

    # Genera calendario
    calendar = generate_italian_league_calendar(season, teams_by_level)

    summary = calendar.get_calendar_summary()
    print(f"Partite totali programmate: {summary['total_matches']}")
    print(f"Competizioni: {summary['competitions']}")

    # Mostra alcune partite
    next_matches = calendar.get_next_matches(limit=5)
    print(f"\nPrime 5 partite programmate:")
    for match in next_matches:
        print(f"  G{match.matchday}: {match.home_team} vs {match.away_team} - {match.date}")

    return season, competition_manager, calendar

def test_match_simulation():
    """Test simulazione partite"""
    print("\n=== TEST SIMULAZIONE PARTITE ===")

    season, competition_manager, calendar = test_calendar_generation()

    # Genera giocatori per alcune squadre
    print("Generando giocatori per test...")
    players_by_team = {}

    # Prendi prime 4 squadre
    all_teams = set()
    for comp in competition_manager.get_all_competitions():
        all_teams.update(comp.teams)

    test_teams = list(all_teams)[:4]

    for team_name in test_teams:
        # Genera rosa
        squad = player_generator.generate_team_squad(team_level=2, squad_size=25)

        # Assegna squadra ai giocatori
        for player in squad:
            player.current_club = team_name

        players_by_team[team_name] = squad
        print(f"  {team_name}: {len(squad)} giocatori")

    # Simula prima giornata
    print(f"\nSimulando giornata 1...")
    season.current_matchday = 0

    results = competition_manager.simulate_all_matchdays(1)

    print(f"Risultati simulati:")
    for comp_name, matches in results.items():
        print(f"\n{comp_name}:")
        for match in matches:
            print(f"  {match['home_team']} {match['home_score']}-{match['away_score']} {match['away_team']}")

    return season, competition_manager, calendar

def test_standings():
    """Test classifiche"""
    print("\n=== TEST CLASSIFICHE ===")

    season, competition_manager, calendar = test_match_simulation()

    # Simula più giornate
    print("Simulando 5 giornate...")

    for matchday in range(2, 7):
        results = competition_manager.simulate_all_matchdays(matchday)
        print(f"Giornata {matchday}: {sum(len(matches) for matches in results.values())} partite")

    # Mostra classifiche
    standings = competition_manager.get_current_standings()

    for comp_name, table in standings.items():
        print(f"\nClassifica {comp_name}:")
        for standing in table[:5]:  # Prime 5
            print(f"  {standing.position:2d}. {standing.team_name:<15} "
                  f"{standing.points:2d} pt ({standing.wins}-{standing.draws}-{standing.losses})")

def test_season_progress():
    """Test progressione stagione completa"""
    print("\n=== TEST PROGRESSIONE STAGIONE ===")

    season = Season(2025)

    print(f"Inizio stagione: {season.get_formatted_date()}")

    # Simula avanzamento fino inizio campionato
    days_to_start = season.days_until_season_start
    print(f"Avanzando {days_to_start} giorni fino inizio campionato...")

    events = season.advance_date(days_to_start)
    print(f"Eventi durante avanzamento: {len(events)}")

    print(f"Data attuale: {season.get_formatted_date()}")
    print(f"Fase: {season.current_phase.value}")
    print(f"Progresso stagione: {season.season_progress_percentage:.1f}%")

    # Prossimo evento importante
    next_date, next_event = season.get_next_important_date()
    print(f"Prossimo evento: {next_event} ({next_date})")

if __name__ == "__main__":
    try:
        ensure_directories()

        # Carica dati necessari
        if not data_loader.load_all_data():
            print("Errore: impossibile caricare i dati")
            sys.exit(1)

        # Esegui test
        test_season_creation()
        test_competitions_setup()
        test_calendar_generation()
        test_match_simulation()
        test_standings()
        test_season_progress()

        print("\n=== TUTTI I TEST COMPETIZIONI COMPLETATI ===")

    except Exception as e:
        print(f"Errore durante i test: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)