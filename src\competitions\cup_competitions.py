"""
Gestione competizioni a eliminazione diretta italiane
Coppa Italia, Supercoppa Italiana, Coppa Italia Serie C
"""
from typing import List, Dict, Optional, Set
from dataclasses import dataclass, field
from enum import Enum
import random
from datetime import date, datetime

from ..core.utils import logger
from ..core.config import *
from .competition import Competition, CompetitionType
from .season import Season
from .calendar import Match
from .match_engine import match_simulator

class CupType(Enum):
    """Tipi di coppa italiana"""
    COPPA_ITALIA = "Coppa Italia"
    SUPERCOPPA = "Supercoppa Italiana"
    COPPA_ITALIA_SERIE_C = "Coppa Italia Serie C"

class CupRound(Enum):
    """Fasi coppe italiane"""
    # Coppa Italia
    PRELIMINARY = "Turno Preliminare"
    FIRST_ROUND = "Primo Turno"
    SECOND_ROUND = "Secondo Turno"
    ROUND_16 = "Sedicesimi"
    QUARTER_FINALS = "Quarti di Finale"
    SEMI_FINALS = "Semifinali"
    FINAL = "Finale"

    # Supercoppa
    SUPER_SEMI_1 = "Semifinale 1"
    SUPER_SEMI_2 = "Semifinale 2"
    SUPER_FINAL = "Finale Supercoppa"

@dataclass
class CupMatch(Match):
    """Match di coppa con eliminazione diretta"""
    round_name: str = ""
    is_home_leg: bool = True  # Per semifinali A/R
    aggregate_score: Optional[tuple] = None  # (casa, trasferta) aggregate
    winner: Optional[str] = None
    decided_by_penalties: bool = False

class CupCompetition(Competition):
    """Competizione a eliminazione diretta"""

    def __init__(self, name: str, season: Season, cup_type: CupType):
        super().__init__(name, season, [])  # Teams aggiunte dinamicamente
        self.cup_type = cup_type
        self.competition_type = CompetitionType.CUP
        self.current_round = None
        self.qualified_teams: Dict[str, List[str]] = {}  # round -> teams
        self.eliminated_teams: Set[str] = set()
        self.winner = None

        logger.info(f"Creata coppa: {name} ({cup_type.value})")

    def add_teams_for_round(self, round_name: str, teams: List[str]):
        """Aggiunge squadre per una specifica fase"""
        self.qualified_teams[round_name] = teams.copy()
        # Aggiorna teams complessivo
        for team in teams:
            if team not in self.teams:
                self.teams.append(team)

    def get_teams_for_round(self, round_name: str) -> List[str]:
        """Restituisce squadre qualificate per una fase"""
        return self.qualified_teams.get(round_name, [])

    def simulate_round(self, round_name: str) -> List[Dict]:
        """Simula una fase completa"""
        teams = self.get_teams_for_round(round_name)
        if len(teams) < 2:
            logger.warning(f"Squadre insufficienti per {round_name}: {len(teams)}")
            return []

        results = []
        winners = []

        # Genera accoppiamenti
        pairings = self._generate_pairings(teams)

        for home_team, away_team in pairings:
            if self._is_two_leg_round(round_name):
                # Andata e ritorno
                result = self._simulate_two_leg_tie(home_team, away_team, round_name)
            else:
                # Gara secca
                result = self._simulate_single_match(home_team, away_team, round_name)

            results.append(result)
            winners.append(result["winner"])

        # Qualifica vincenti al turno successivo
        next_round = self._get_next_round(round_name)
        if next_round and winners:
            self.qualified_teams[next_round] = winners

        self.current_round = round_name
        logger.info(f"{self.name} - {round_name}: {len(results)} eliminazioni simulate")

        return results

    def _generate_pairings(self, teams: List[str]) -> List[tuple]:
        """Genera accoppiamenti casuali"""
        teams_copy = teams.copy()
        random.shuffle(teams_copy)

        pairings = []
        for i in range(0, len(teams_copy), 2):
            if i + 1 < len(teams_copy):
                pairings.append((teams_copy[i], teams_copy[i + 1]))

        return pairings

    def _simulate_single_match(self, home_team: str, away_team: str, round_name: str) -> Dict:
        """Simula partita secca usando MatchSimulator unificato"""
        # Crea match temporaneo per simulazione
        match = Match(
            matchday=0,  # Non applicabile per coppe
            home_team=home_team,
            away_team=away_team,
            date=date.today(),
            time="18:00",
            competition=f"{self.name} - {round_name}",
            venue=f"Stadio {home_team}"
        )

        # Ottieni giocatori delle squadre (se disponibili)
        home_players = self.get_team_players(home_team)
        away_players = self.get_team_players(away_team)

        # Usa MatchSimulator unificato
        result = match_simulator.simulate_match(match, home_players, away_players, "cup")

        # Determina vincitore (per coppa deve esserci sempre)
        home_goals = result["home_score"]
        away_goals = result["away_score"]
        winner = home_team if home_goals > away_goals else away_team if away_goals > home_goals else None

        decided_by_penalties = result.get("decided_by_penalties", False)
        if winner is None:
            # In caso di pareggio, determina vincitore ai rigori
            winner = random.choice([home_team, away_team])
            decided_by_penalties = True

        # Aggiorna eliminati
        loser = away_team if winner == home_team else home_team
        self.eliminated_teams.add(loser)

        return {
            "match_id": result["match_id"],
            "round": round_name,
            "home_team": home_team,
            "away_team": away_team,
            "home_score": home_goals,
            "away_score": away_goals,
            "winner": winner,
            "loser": loser,
            "decided_by_penalties": decided_by_penalties,
            "attendance": result.get("attendance", random.randint(15000, 70000)),
            "date": result.get("date", date.today()),
            "events": result.get("events", [])
        }

    def _simulate_two_leg_tie(self, team1: str, team2: str, round_name: str) -> Dict:
        """Simula eliminatoria A/R usando MatchSimulator unificato"""
        # Andata: team1 in casa
        first_leg_match = Match(
            matchday=0,
            home_team=team1,
            away_team=team2,
            date=date.today(),
            time="18:00",
            competition=f"{self.name} - {round_name} - Andata",
            venue=f"Stadio {team1}"
        )

        home_players_1 = self.get_team_players(team1)
        away_players_1 = self.get_team_players(team2)
        first_leg_result = match_simulator.simulate_match(first_leg_match, home_players_1, away_players_1, "cup")

        # Ritorno: team2 in casa
        second_leg_match = Match(
            matchday=0,
            home_team=team2,
            away_team=team1,
            date=date.today(),
            time="18:00",
            competition=f"{self.name} - {round_name} - Ritorno",
            venue=f"Stadio {team2}"
        )

        home_players_2 = self.get_team_players(team2)
        away_players_2 = self.get_team_players(team1)
        second_leg_result = match_simulator.simulate_match(second_leg_match, home_players_2, away_players_2, "cup")

        # Calcola aggregate
        home1_goals = first_leg_result["home_score"]
        away1_goals = first_leg_result["away_score"]
        home2_goals = second_leg_result["home_score"]
        away2_goals = second_leg_result["away_score"]

        team1_aggregate = home1_goals + away2_goals
        team2_aggregate = away1_goals + home2_goals

        winner = team1 if team1_aggregate > team2_aggregate else team2 if team2_aggregate > team1_aggregate else None

        decided_by_penalties = False
        if winner is None:
            # Rigori nella gara di ritorno
            winner = random.choice([team1, team2])
            decided_by_penalties = True

        # Aggiorna eliminati
        loser = team2 if winner == team1 else team1
        self.eliminated_teams.add(loser)

        return {
            "match_id": f"{team1}_vs_{team2}_{round_name}_aggregate",
            "round": round_name,
            "team1": team1,
            "team2": team2,
            "first_leg": {
                "home": team1,
                "away": team2,
                "home_score": home1_goals,
                "away_score": away1_goals,
                "events": first_leg_result.get("events", [])
            },
            "second_leg": {
                "home": team2,
                "away": team1,
                "home_score": home2_goals,
                "away_score": away2_goals,
                "events": second_leg_result.get("events", [])
            },
            "aggregate": (team1_aggregate, team2_aggregate),
            "winner": winner,
            "loser": loser,
            "decided_by_penalties": decided_by_penalties
        }

    def _is_two_leg_round(self, round_name: str) -> bool:
        """Verifica se è una fase A/R"""
        two_leg_rounds = [CupRound.SEMI_FINALS.value]
        return round_name in two_leg_rounds

    def _get_next_round(self, current_round: str) -> Optional[str]:
        """Restituisce fase successiva"""
        if self.cup_type == CupType.COPPA_ITALIA:
            round_progression = {
                CupRound.PRELIMINARY.value: CupRound.FIRST_ROUND.value,
                CupRound.FIRST_ROUND.value: CupRound.SECOND_ROUND.value,
                CupRound.SECOND_ROUND.value: CupRound.ROUND_16.value,
                CupRound.ROUND_16.value: CupRound.QUARTER_FINALS.value,
                CupRound.QUARTER_FINALS.value: CupRound.SEMI_FINALS.value,
                CupRound.SEMI_FINALS.value: CupRound.FINAL.value,
                CupRound.FINAL.value: None
            }
        elif self.cup_type == CupType.SUPERCOPPA:
            round_progression = {
                CupRound.SUPER_SEMI_1.value: CupRound.SUPER_FINAL.value,
                CupRound.SUPER_SEMI_2.value: CupRound.SUPER_FINAL.value,
                CupRound.SUPER_FINAL.value: None
            }
        else:  # Serie C
            round_progression = {
                CupRound.FIRST_ROUND.value: CupRound.SECOND_ROUND.value,
                CupRound.SECOND_ROUND.value: CupRound.QUARTER_FINALS.value,
                CupRound.QUARTER_FINALS.value: CupRound.SEMI_FINALS.value,
                CupRound.SEMI_FINALS.value: CupRound.FINAL.value,
                CupRound.FINAL.value: None
            }

        return round_progression.get(current_round)

    def is_completed(self) -> bool:
        """Verifica se la coppa è terminata"""
        return self.winner is not None

    def get_competition_stats(self) -> Dict:
        """Restituisce statistiche coppa"""
        return {
            "name": self.name,
            "type": self.cup_type.value,
            "current_round": self.current_round,
            "teams_remaining": len(self.teams) - len(self.eliminated_teams),
            "teams_eliminated": len(self.eliminated_teams),
            "winner": self.winner,
            "completed": self.is_completed()
        }

class CoppaItaliaCompetition(CupCompetition):
    """Coppa Italia con struttura realistica"""

    def __init__(self, season: Season):
        super().__init__("Coppa Italia", season, CupType.COPPA_ITALIA)
        self.setup_tournament_structure()

    def setup_tournament_structure(self):
        """Setup struttura torneo Coppa Italia"""
        # Squadre per fase (da determinare dinamicamente)
        self.rounds_structure = {
            CupRound.PRELIMINARY.value: 8,      # 8 squadre Serie C più basse
            CupRound.FIRST_ROUND.value: 32,     # 4 vinc. prelim. + 16 Serie B + 12 Serie A (9°-20°)
            CupRound.SECOND_ROUND.value: 16,    # 16 vinc. primo turno
            CupRound.ROUND_16.value: 16,        # 8 vinc. secondo turno + 8 migliori Serie A (1°-8°)
            CupRound.QUARTER_FINALS.value: 8,   # 8 vinc. sedicesimi
            CupRound.SEMI_FINALS.value: 4,      # 4 vinc. quarti
            CupRound.FINAL.value: 2             # 2 vinc. semifinali
        }

class SupercoppaItalianaCompetition(CupCompetition):
    """Supercoppa Italiana formato Final Four"""

    def __init__(self, season: Season):
        super().__init__("Supercoppa Italiana", season, CupType.SUPERCOPPA)
        self.setup_teams()

    def setup_teams(self):
        """Setup squadre qualificate per Supercoppa"""
        # Squadre qualificate in base ai risultati precedenti
        qualified_teams = [
            SERIE_A_CHAMPION_2024,      # Napoli (Campione Serie A)
            SERIE_A_RUNNER_UP_2024,     # Inter Milan (Vice-campione Serie A)
            COPPA_ITALIA_WINNER_2024,   # Bologna (Vincitore Coppa Italia)
            COPPA_ITALIA_FINALIST_2024  # Atalanta (Finalista Coppa Italia)
        ]

        # Semifinali
        self.add_teams_for_round(CupRound.SUPER_SEMI_1.value, [qualified_teams[0], qualified_teams[2]])  # Napoli vs Bologna
        self.add_teams_for_round(CupRound.SUPER_SEMI_2.value, [qualified_teams[1], qualified_teams[3]])  # Inter vs Atalanta

        logger.info(f"Supercoppa: Qualificate {qualified_teams}")

class CoppaItaliaSerieCCompetition(CupCompetition):
    """Coppa Italia Serie C"""

    def __init__(self, season: Season, serie_c_teams: List[str]):
        super().__init__("Coppa Italia Serie C", season, CupType.COPPA_ITALIA_SERIE_C)
        self.setup_serie_c_tournament(serie_c_teams)

    def setup_serie_c_tournament(self, serie_c_teams: List[str]):
        """Setup torneo Serie C con 56/60 squadre"""
        # Prende prime 56 squadre (4 partecipano anche alla Coppa Italia principale)
        participating_teams = serie_c_teams[:56] if len(serie_c_teams) >= 56 else serie_c_teams

        # Primo turno con tutte le squadre partecipanti
        self.add_teams_for_round(CupRound.FIRST_ROUND.value, participating_teams)

        logger.info(f"Coppa Italia Serie C: {len(participating_teams)} squadre partecipanti")

def setup_italian_cups(season: Season, teams_by_level: Dict[int, List[str]],
                      serie_c_groups: Dict[str, List[str]]) -> Dict[str, CupCompetition]:
    """Setup completo delle coppe italiane"""
    cups = {}

    # Coppa Italia
    coppa_italia = CoppaItaliaCompetition(season)
    cups["Coppa Italia"] = coppa_italia

    # Supercoppa Italiana
    supercoppa = SupercoppaItalianaCompetition(season)
    cups["Supercoppa Italiana"] = supercoppa

    # Coppa Italia Serie C
    all_serie_c_teams = []
    for teams in serie_c_groups.values():
        all_serie_c_teams.extend(teams)

    if all_serie_c_teams:
        coppa_italia_c = CoppaItaliaSerieCCompetition(season, all_serie_c_teams)
        cups["Coppa Italia Serie C"] = coppa_italia_c

    logger.info(f"Coppe italiane create: {list(cups.keys())}")
    return cups