"""
Configurazioni e costanti per Football Manager Italiano
"""
import os
from pathlib import Path

# Percorsi base
BASE_DIR = Path(__file__).parent.parent.parent
DATA_DIR = BASE_DIR / "data"
SAVES_DIR = DATA_DIR / "saves"
ASSETS_DIR = BASE_DIR / "assets"

# Percorsi dati JSON
LEAGUES_DIR = BASE_DIR
PLAYER_NAMES_FILE = BASE_DIR / "player_names.json"
FIFA_CODES_FILE = BASE_DIR / "fifa_country_codes.json"

# Configurazioni database
DB_NAME = "football_manager.db"
DB_PATH = DATA_DIR / DB_NAME

# Configurazioni interfaccia grafica
WINDOW_TITLE = "Football Manager Italiano"
WINDOW_MIN_WIDTH = 1200
WINDOW_MIN_HEIGHT = 800
WINDOW_DEFAULT_WIDTH = 1400
WINDOW_DEFAULT_HEIGHT = 900

# Font e colori tema chiaro
MAIN_FONT_FAMILY = "Segoe UI"
MAIN_FONT_SIZE = 10
HEADER_FONT_SIZE = 12
TITLE_FONT_SIZE = 14

# Colori tema chiaro
COLOR_PRIMARY = "#2E7D32"        # Verde calcio
COLOR_SECONDARY = "#4CAF50"      # Verde chiaro
COLOR_BACKGROUND = "#FFFFFF"     # Bianco
COLOR_SURFACE = "#F5F5F5"        # Grigio molto chiaro
COLOR_BORDER = "#E0E0E0"         # Grigio bordi
COLOR_TEXT = "#212121"           # Nero testo
COLOR_TEXT_SECONDARY = "#757575"  # Grigio testo secondario
COLOR_SUCCESS = "#4CAF50"        # Verde successo
COLOR_WARNING = "#FF9800"        # Arancione warning
COLOR_ERROR = "#F44336"          # Rosso errore

# Configurazioni gioco
SEASONS_TO_SIMULATE = 30
PLAYERS_PER_TEAM = 25
MIN_PLAYERS_PER_TEAM = 16
MAX_PLAYERS_PER_TEAM = 30

# Campionati italiani
ITALIAN_LEAGUES = {
    "Serie A": {"level": 1, "teams": 20, "promoted_to": None, "relegated_to": "Serie B"},
    "Serie B": {"level": 2, "teams": 20, "promoted_to": "Serie A", "relegated_to": "Serie C"},
    "Serie C": {"level": 3, "teams": 60, "promoted_to": "Serie B", "relegated_to": None}  # 3 gironi da 20
}

# Competizioni italiane - Dati stagione precedente 2024/25
COPPA_ITALIA_WINNER_2024 = "Bologna"
SERIE_A_CHAMPION_2024 = "Napoli"
SERIE_A_RUNNER_UP_2024 = "Inter Milan"
COPPA_ITALIA_FINALIST_2024 = "Atalanta"

# Configurazioni finanziarie (in euro)
STARTING_BUDGETS = {
    1: {"transfer": 50000000, "salary": 4000000},    # Serie A
    2: {"transfer": 8000000, "salary": 800000},      # Serie B
    3: {"transfer": 1500000, "salary": 200000}       # Serie C
}

# Strutture e facilities
FACILITY_LEVELS = {
    "stadio": {"min": 1, "max": 10, "cost_per_level": 5000000},
    "allenamento": {"min": 1, "max": 10, "cost_per_level": 2000000},
    "giovanili": {"min": 1, "max": 10, "cost_per_level": 1500000},
    "medico": {"min": 1, "max": 10, "cost_per_level": 1000000}
}

# Attributi giocatori (range 1-20)
PLAYER_ATTRIBUTES = [
    "tecnica", "velocita", "forza", "resistenza", "tiro",
    "passaggio", "dribbling", "difesa", "portiere", "mentalita"
]

# Ruoli giocatori
PLAYER_POSITIONS = {
    "P": "Portiere",
    "DC": "Difensore Centrale",
    "DS": "Difensore Sinistro",
    "DD": "Difensore Destro",
    "CC": "Centrocampista Centrale",
    "CS": "Centrocampista Sinistro",
    "CD": "Centrocampista Destro",
    "T": "Trequartista",
    "AS": "Attaccante Sinistro",
    "AD": "Attaccante Destro",
    "C": "Centravanti"
}

# Età giocatori
PLAYER_AGE_RANGES = {
    "giovane": (16, 21),
    "adulto": (22, 29),
    "veterano": (30, 38)
}

# Configurazioni staff
STAFF_TYPES = {
    "allenatore": {"max_level": 20, "salary_base": 500000},
    "vice_allenatore": {"max_level": 15, "salary_base": 200000},
    "preparatore_atletico": {"max_level": 15, "salary_base": 100000},
    "preparatore_portieri": {"max_level": 15, "salary_base": 80000},
    "medico": {"max_level": 15, "salary_base": 120000},
    "fisioterapista": {"max_level": 10, "salary_base": 60000},
    "scout": {"max_level": 15, "salary_base": 80000},
    "direttore_sportivo": {"max_level": 20, "salary_base": 400000}
}

# Configurazioni tempo di gioco
GAME_SPEEDS = {
    "lento": 2000,      # ms tra aggiornamenti
    "normale": 1000,
    "veloce": 500,
    "molto_veloce": 200
}

# Configurazioni match engine
MATCH_EVENTS = [
    "gol", "cartellino_giallo", "cartellino_rosso",
    "infortunio", "sostituzione", "rigore"
]

# Testi interfaccia in italiano
UI_TEXTS = {
    "menu": {
        "file": "File",
        "nuovo_gioco": "Nuova Partita",
        "carica": "Carica Partita",
        "salva": "Salva Partita",
        "esci": "Esci"
    },
    "schermate": {
        "panoramica": "Panoramica Club",
        "squadra": "Squadra",
        "trasferimenti": "Trasferimenti",
        "finanze": "Finanze",
        "competizioni": "Competizioni",
        "staff": "Staff"
    },
    "generale": {
        "conferma": "Conferma",
        "annulla": "Annulla",
        "si": "Sì",
        "no": "No",
        "euro": "€",
        "milioni": "M€",
        "migliaia": "K€"
    }
}

# Configurazioni logging
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_FILE = DATA_DIR / "football_manager.log"

def ensure_directories():
    """Crea le directory necessarie se non esistono"""
    os.makedirs(DATA_DIR, exist_ok=True)
    os.makedirs(SAVES_DIR, exist_ok=True)
    os.makedirs(ASSETS_DIR, exist_ok=True)

def get_league_file_path(country: str, league_name: str) -> Path:
    """Restituisce il percorso del file JSON per un campionato"""
    return BASE_DIR / country / f"{league_name}.json"

def format_currency(amount: int) -> str:
    """Formatta un importo in euro in formato italiano"""
    if amount >= 1000000:
        return f"{amount/1000000:.1f}M€"
    elif amount >= 1000:
        return f"{amount/1000:.0f}K€"
    else:
        return f"{amount}€"

def format_date_italian(date) -> str:
    """Formatta una data in formato italiano"""
    months = [
        "gennaio", "febbraio", "marzo", "aprile", "maggio", "giugno",
        "luglio", "agosto", "settembre", "ottobre", "novembre", "dicembre"
    ]
    return f"{date.day} {months[date.month-1]} {date.year}"