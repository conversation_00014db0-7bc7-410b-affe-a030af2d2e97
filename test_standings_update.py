#!/usr/bin/env python3
"""
Test per verificare che le classifiche vengano aggiornate correttamente
quando le partite vengono simulate automaticamente.
"""

import sys
import os
from pathlib import Path

# Aggiunge src al path per import
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from src.competitions.season import Season
from src.competitions.competition import CompetitionManager, setup_italian_competitions
from src.competitions.calendar import MatchCalendar, generate_italian_league_calendar
from src.data.data_loader import data_loader
from src.core.utils import logger
import datetime

def test_standings_update():
    """Test aggiornamento classifiche durante simulazione automatica"""
    print("=== TEST AGGIORNAMENTO CLASSIFICHE ===")
    
    # Carica dati
    data_loader.load_all_data()

    # Crea stagione
    season = Season(2025)

    # Crea competition manager con competizioni italiane
    teams_by_level = {
        1: [team["nome"] for team in data_loader.get_serie_a_teams()],
        2: [team["nome"] for team in data_loader.get_serie_b_teams()]
    }
    serie_c_groups = data_loader.get_serie_c_groups()
    competition_manager = setup_italian_competitions(season, teams_by_level, serie_c_groups)
    
    # Genera calendario
    calendar = generate_italian_league_calendar(season, teams_by_level)
    
    print(f"Calendario creato con {len(calendar.matches)} partite")
    
    # Mostra classifiche iniziali (tutte a 0)
    print("\n=== CLASSIFICHE INIZIALI ===")
    standings = competition_manager.get_current_standings()
    for comp_name, table in standings.items():
        if comp_name == "Serie A":  # Solo Serie A per brevità
            print(f"\n{comp_name}:")
            for i, standing in enumerate(table[:5]):
                print(f"  {standing.position:2d}. {standing.team_name:<15} "
                      f"{standing.points:2d} pt ({standing.wins}-{standing.draws}-{standing.losses})")
    
    # Avanza alla data della prima partita
    print(f"\nData corrente: {season.current_date}")
    
    # Trova la prima partita
    first_matches = sorted([m for m in calendar.matches if not m.played], key=lambda x: x.date)
    if first_matches:
        first_match_date = first_matches[0].date
        print(f"Prima partita il: {first_match_date}")
        
        # Avanza fino alla data della prima partita
        days_to_advance = (first_match_date - season.current_date).days
        if days_to_advance > 0:
            print(f"Avanzando di {days_to_advance} giorni...")
            
            # Simula l'avanzamento come nel Continue Widget
            start_date = season.current_date
            season.advance_date(days_to_advance)
            end_date = season.current_date
            
            # Simula automaticamente le partite nel range
            matches_in_range = calendar.get_matches_in_date_range(start_date, end_date)
            matches_simulated = 0
            
            for match in matches_in_range:
                if not match.played and match.date <= end_date:
                    # Trova la competizione per questa partita
                    competition = competition_manager.get_competition(match.competition)
                    if competition and hasattr(competition, 'simulate_match'):
                        result = competition.simulate_match(match)
                        if "error" not in result:
                            matches_simulated += 1
                            print(f"  Simulata: {match.home_team} {match.home_score}-{match.away_score} {match.away_team}")
            
            print(f"Simulate {matches_simulated} partite")
    
    # Mostra classifiche dopo simulazione
    print("\n=== CLASSIFICHE DOPO SIMULAZIONE ===")
    standings = competition_manager.get_current_standings()
    for comp_name, table in standings.items():
        if comp_name == "Serie A":  # Solo Serie A per brevità
            print(f"\n{comp_name}:")
            for i, standing in enumerate(table[:5]):
                print(f"  {standing.position:2d}. {standing.team_name:<15} "
                      f"{standing.points:2d} pt ({standing.wins}-{standing.draws}-{standing.losses}) "
                      f"GF:{standing.goals_for} GS:{standing.goals_against}")
    
    # Verifica che almeno una squadra abbia punti
    serie_a_standings = standings.get("Serie A", [])
    total_points = sum(s.points for s in serie_a_standings)
    total_matches = sum(s.matches_played for s in serie_a_standings)
    
    print(f"\nRiepilogo:")
    print(f"- Punti totali assegnati: {total_points}")
    print(f"- Partite totali giocate: {total_matches}")
    
    if total_points > 0:
        print("✅ TEST SUPERATO: Le classifiche sono state aggiornate!")
    else:
        print("❌ TEST FALLITO: Le classifiche non sono state aggiornate!")
    
    return total_points > 0

if __name__ == "__main__":
    success = test_standings_update()
    sys.exit(0 if success else 1)
