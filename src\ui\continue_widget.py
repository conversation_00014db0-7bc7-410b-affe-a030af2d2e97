"""
Widget Continue semplificato per Football Manager <PERSON><PERSON>
Utiliz<PERSON> il GameEngine per l'avanzamento del tempo
"""
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QProgressBar, QFrame, QListWidget, QListWidgetItem)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QColor

from ..core.config import *
from ..core.utils import logger, format_date_italian
from ..core.game_engine import GameEngine

class ContinueWidget(QWidget):
    """Widget semplificato per controllo avanzamento tempo tramite GameEngine"""
    
    # Segnali
    simulation_requested = pyqtSignal()
    simulation_completed = pyqtSignal(str, dict)  # reason, data
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.game_engine = None
        
        self.setup_ui()
        
    def setup_ui(self):
        """Configura interfaccia widget"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Header con data corrente
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        header_frame.setStyleSheet(f"background-color: {COLOR_PRIMARY}; color: white; border-radius: 5px;")
        header_layout = QVBoxLayout(header_frame)
        
        # Data corrente
        self.date_label = QLabel("Data: --/--/----")
        self.date_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        self.date_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(self.date_label)
        
        # Giornata corrente
        self.matchday_label = QLabel("Giornata: --")
        self.matchday_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(self.matchday_label)
        
        layout.addWidget(header_frame)
        
        # Pulsante Continue principale
        self.continue_button = QPushButton("Continue")
        self.continue_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLOR_SUCCESS};
                color: white;
                font-weight: bold;
                font-size: 16px;
                padding: 15px;
                border-radius: 8px;
                border: none;
            }}
            QPushButton:hover {{
                background-color: #45a049;
            }}
            QPushButton:pressed {{
                background-color: #3d8b40;
            }}
            QPushButton:disabled {{
                background-color: #cccccc;
                color: #666666;
            }}
        """)
        self.continue_button.clicked.connect(self.on_continue_clicked)
        layout.addWidget(self.continue_button)
        
        # Progress bar per simulazione
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Lista eventi prossimi
        events_frame = QFrame()
        events_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        events_layout = QVBoxLayout(events_frame)
        
        events_title = QLabel("Prossimi Eventi")
        events_title.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        events_layout.addWidget(events_title)
        
        self.events_list = QListWidget()
        self.events_list.setMaximumHeight(150)
        events_layout.addWidget(self.events_list)
        
        layout.addWidget(events_frame)
        
        # Status
        self.status_label = QLabel("Pronto")
        self.status_label.setStyleSheet("color: #666; font-style: italic;")
        layout.addWidget(self.status_label)
        
        layout.addStretch()
        
    def set_game_engine(self, game_engine: GameEngine):
        """Imposta il GameEngine e collega i segnali"""
        self.game_engine = game_engine
        
        # Collega i segnali del GameEngine
        self.game_engine.date_changed.connect(self.on_date_changed)
        self.game_engine.simulation_stopped.connect(self.on_simulation_stopped)
        
        # Aggiorna display iniziale
        self.update_display()
        

    def on_date_changed(self, new_date):
        """Gestisce cambio data dal GameEngine"""
        self.update_display()
        
    def on_continue_clicked(self):
        """Gestisce click sul pulsante Continue"""
        logger.info("=== CONTINUE BUTTON CLICKED ===")

        if not self.game_engine:
            logger.error("GameEngine non disponibile!")
            self.status_label.setText("ERRORE: GameEngine non trovato")
            return

        # Debug: controlla stato GameEngine
        status = self.game_engine.get_simulation_status()
        logger.info(f"GameEngine status: {status}")

        next_stop = self.game_engine.get_next_stop_point()
        if next_stop:
            logger.info(f"Next stop point: {next_stop.date} - {next_stop.description}")
        else:
            logger.error("NESSUN STOP POINT TROVATO!")
            self.status_label.setText("ERRORE: Nessun evento futuro")
            return

        # Disabilita pulsante durante simulazione
        self.continue_button.setEnabled(False)
        self.continue_button.setText("Simulazione in corso...")
        self.progress_bar.setVisible(True)
        self.status_label.setText("Simulazione in corso...")

        # Emetti segnale per avviare simulazione
        self.simulation_requested.emit()

        # Avvia simulazione nel GameEngine
        logger.info("Avvio GameEngine simulation...")
        try:
            self.game_engine.run_simulation_until_next_stop()
        except Exception as e:
            logger.error(f"ERRORE GameEngine: {e}")
            self.status_label.setText(f"ERRORE: {e}")
            self.continue_button.setEnabled(True)
            self.continue_button.setText("Continue")
            self.progress_bar.setVisible(False)

    def on_simulation_stopped(self, reason: str, data: dict):
        """Gestisce fine simulazione dal GameEngine"""
        self.continue_button.setEnabled(True)
        self.continue_button.setText("Continue")
        self.progress_bar.setVisible(False)
        
        # Aggiorna status
        if reason == "PLAYER_MATCH":
            self.status_label.setText("Partita della tua squadra!")
        elif reason == "IMPORTANT_MATCH":
            self.status_label.setText("Big match in programma")
        elif reason == "TRANSFER_WINDOW":
            self.status_label.setText("Finestra di mercato")
        else:
            self.status_label.setText(f"Fermato: {reason}")
            
        # Aggiorna display
        self.update_display()
        
        # Emetti segnale di completamento
        self.simulation_completed.emit(reason, data)
        
    def update_display(self):
        """Aggiorna visualizzazione corrente"""
        if not self.game_engine or not self.game_engine.season:
            return
            
        # Aggiorna data
        current_date = self.game_engine.season.get_formatted_date()
        self.date_label.setText(f"Data: {current_date}")
        
        # Aggiorna giornata (se disponibile)
        if hasattr(self.game_engine, 'competition_manager') and self.game_engine.competition_manager:
            # Trova la giornata corrente della Serie A
            serie_a = self.game_engine.competition_manager.get_competition("Serie A")
            if serie_a:
                current_md = getattr(serie_a, 'current_matchday', 1)
                self.matchday_label.setText(f"Giornata: {current_md}")
        
        # Aggiorna lista eventi
        self.update_events_list()
        
    def update_events_list(self):
        """Aggiorna lista prossimi eventi"""
        self.events_list.clear()
        
        if not self.game_engine:
            return
            
        # Ottieni prossimi eventi
        upcoming_events = self.game_engine.get_upcoming_events(limit=5)

        for event in upcoming_events:
            date_str = event["date"].strftime("%d/%m/%Y")

            if event["type"] == "PLAYER_MATCH":
                text = f"🏆 {date_str} - Partita della tua squadra"
                color = QColor(255, 255, 224)  # Light yellow
            elif event["type"] == "IMPORTANT_MATCH":
                text = f"⚽ {date_str} - Big match"
                color = QColor(224, 255, 224)  # Light green
            elif event["type"] == "TRANSFER_WINDOW":
                text = f"💰 {date_str} - Mercato"
                color = QColor(255, 224, 224)  # Light red
            else:
                text = f"📅 {date_str} - {event['description']}"
                color = QColor(240, 240, 240)  # Light gray

            item = QListWidgetItem(text)
            item.setBackground(color)
            self.events_list.addItem(item)
