"""
Modulo UI per le competizioni - Struttura modulare
"""

from .competitions_ui import CompetitionsUI
from .dialogs.match_details_dialog import MatchDetailsDialog
from .widgets.standings_table import StandingsTableWidget
from .widgets.matches_table import MatchesTableWidget
from .widgets.season_info import SeasonInfoWidget
from .tabs.competition_tab import CompetitionTabWidget

__all__ = [
    'CompetitionsUI',
    'MatchDetailsDialog', 
    'StandingsTableWidget',
    'MatchesTableWidget',
    'SeasonInfoWidget',
    'CompetitionTabWidget'
]
