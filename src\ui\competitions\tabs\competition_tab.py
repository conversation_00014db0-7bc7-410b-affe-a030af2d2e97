"""
Tab per singola competizione (campionato)
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, 
    QSplitter, QLabel, QComboBox
)
from PyQt6.QtCore import Qt
from typing import Optional
from ..widgets.standings_table import StandingsTableWidget
from ..widgets.matches_table import MatchesTableWidget


class CompetitionTabWidget(QWidget):
    """Widget tab per singola competizione"""
    
    def __init__(self, competition_name: str, parent=None):
        super().__init__(parent)
        self.competition_name = competition_name
        self.standings_table: Optional[StandingsTableWidget] = None
        self.matches_table: Optional[MatchesTableWidget] = None
        self.team_filter: Optional[QComboBox] = None
        self.setup_ui()
    
    def setup_ui(self):
        """Configura l'interfaccia della tab competizione"""
        layout = QVBoxLayout(self)
        
        # Splitter principale
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Panel sinistro - Classifica
        standings_group = QGroupBox("Classifica")
        standings_layout = QVBoxLayout(standings_group)
        
        self.standings_table = StandingsTableWidget()
        standings_layout.addWidget(self.standings_table)
        
        main_splitter.addWidget(standings_group)
        
        # Panel destro - Partite
        matches_group = QGroupBox("Partite")
        matches_layout = QVBoxLayout(matches_group)
        
        # Filtro squadra
        filter_layout = QHBoxLayout()
        filter_layout.addWidget(QLabel("Squadra:"))
        
        self.team_filter = QComboBox()
        self.team_filter.addItem("Tutte le squadre")
        filter_layout.addWidget(self.team_filter)
        
        matches_layout.addLayout(filter_layout)
        
        # Tabella partite
        self.matches_table = MatchesTableWidget()
        matches_layout.addWidget(self.matches_table)
        
        main_splitter.addWidget(matches_group)
        
        # Proporzioni splitter
        main_splitter.setSizes([400, 600])
        
        layout.addWidget(main_splitter)
        
        # Connessioni
        self.setup_connections()
    
    def setup_connections(self):
        """Configura connessioni segnali"""
        if self.team_filter and self.matches_table:
            self.team_filter.currentTextChanged.connect(
                self.matches_table.filter_by_team
            )
        
        if self.standings_table:
            self.standings_table.itemSelectionChanged.connect(
                self.on_team_selected_in_standings
            )
    
    def on_team_selected_in_standings(self):
        """Gestisce selezione squadra in classifica"""
        if not self.standings_table or not self.team_filter:
            return
            
        selected_team = self.standings_table.get_selected_team()
        if selected_team:
            # Trova e seleziona la squadra nel filtro
            index = self.team_filter.findText(selected_team)
            if index >= 0:
                self.team_filter.setCurrentIndex(index)
    
    def update_team_filter(self, teams: list):
        """Aggiorna lista squadre nel filtro"""
        if not self.team_filter:
            return
            
        current_selection = self.team_filter.currentText()
        self.team_filter.clear()
        self.team_filter.addItem("Tutte le squadre")
        
        for team in sorted(teams):
            self.team_filter.addItem(team)
        
        # Ripristina selezione se possibile
        index = self.team_filter.findText(current_selection)
        if index >= 0:
            self.team_filter.setCurrentIndex(index)
    
    def get_competition_name(self) -> str:
        """Restituisce nome competizione"""
        return self.competition_name
