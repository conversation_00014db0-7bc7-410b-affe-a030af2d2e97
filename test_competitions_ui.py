"""
Test interfaccia competizioni
"""
import sys
from pathlib import Path

src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

from src.core.config import ensure_directories
from src.data.data_loader import data_loader
from src.ui.competitions_ui import CompetitionsUI

def test_competitions_ui():
    """Test interfaccia competizioni"""
    print("=== TEST INTERFACCIA COMPETIZIONI ===")

    app = QApplication(sys.argv)

    # Carica dati
    ensure_directories()
    if not data_loader.load_all_data():
        print("Errore: impossibile caricare i dati")
        return False

    # Crea interfaccia
    competitions_ui = CompetitionsUI()

    # Mostra finestra
    competitions_ui.show()
    competitions_ui.resize(1200, 800)
    competitions_ui.setWindowTitle("Test Interfaccia Competizioni")

    print("Interfaccia competizioni aperta")
    print("Le competizioni vengono caricate automaticamente all'avvio")

    # Chiudi automaticamente dopo 5 secondi per test
    def close_app():
        competitions_ui.close()
        app.quit()

    QTimer.singleShot(5000, close_app)

    print("Finestra aperta per 5 secondi...")

    app.exec()
    return True

if __name__ == "__main__":
    if test_competitions_ui():
        print("Test interfaccia competizioni completato")
    else:
        print("Test interfaccia competizioni fallito")
        sys.exit(1)